import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Container, Flex, Hide, HStack, Show } from '@chakra-ui/react';
import { Heading } from '@linc-inc/yw-ui/ui';
import { clearChatSlice, setCurrentChatId, useCreateChatMutation } from '@reducers/chat';
import { useCreateMessageMutation } from '@reducers/messages';
import { useChatSettings, useChatStreamStatus } from '@hooks/selectors';
import { NewChatButton } from '@components/chat';
import { ChatMessages } from '@components/chat/chat-messages';
import { BeansIcon } from '@icons';
import { ChatThreads } from '@containers/chat-thread';
import { CHAT_STREAM_URL } from '@constants';
import { EmptyConversation } from './empty-conversation';

export const CHAT_HEADING = 'chat-heading';
export const CHAT_CONTAINER = 'chat-container';

const Chat = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('chat');
  const chatTitle = t('chatTitle');
  const newChat = t('buttons.newChat');
  const { isChatStreamLoading } = useChatStreamStatus();
  const createMessageMutation = useCreateMessageMutation();
  const [createChatMutation] = useCreateChatMutation();
  const { currentChatId } = useChatSettings();
  const isNewChatDisabled = Boolean(!currentChatId) || isChatStreamLoading;

  const handleNewChat = useCallback(() => dispatch(clearChatSlice()), [dispatch]);

  const handleCreateChat = useCallback((chatId: string) => dispatch(setCurrentChatId(chatId)), [dispatch]);

  const createChat = useCallback(() => createChatMutation().unwrap(), [createChatMutation]);

  return (
    <Container data-testid={CHAT_CONTAINER} height="full" maxW="container.xl" paddingEnd={3} paddingStart={3} pb={0}>
      <Flex height="full" maxH="calc(100vh - 64px)">
        <Flex flexDirection="column" height="full" pr={{ base: 0, md: 4, lg: 12 }} w="full">
          <HStack justifyContent="space-between" p={{ base: '16px 0px 12px 0px', md: '32px 0px 16px 0px' }} w="full">
            <Heading data-testid={CHAT_HEADING} variant="h2">
              {chatTitle}
            </Heading>
            <HStack spacing={2}>
              <Hide above="md">
                <ChatThreads />
              </Hide>
              <NewChatButton disabled={isNewChatDisabled} onClick={handleNewChat}>
                {newChat}
              </NewChatButton>
            </HStack>
          </HStack>
          <ChatMessages
            botAvatar={<BeansIcon boxSize={{ base: '3rem', sm: 14 }} />}
            createChatMutation={createChat}
            createMessageMutation={createMessageMutation}
            currentChatId={currentChatId}
            emptyState={<EmptyConversation />}
            streamUrl={CHAT_STREAM_URL}
            onCreateChat={handleCreateChat}
          />
        </Flex>
        <Show above="md">
          <ChatThreads />
        </Show>
      </Flex>
    </Container>
  );
};

export default Chat;
