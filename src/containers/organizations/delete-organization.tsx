import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useBoolean } from '@linc-inc/yw-ui/hooks/common';
import { Button, Flex, Input, Text } from '@linc-inc/yw-ui/ui';
import { useAppForm } from '@hooks/form/form';
import { useDeleteOrgs } from '@hooks/organizations/use-delete-orgs';
import { BaseInput } from '@common/inputs';
import { InfoModal } from '@common/modals';
import { FormWrapper } from '@components/form/form-wrapper';
import {
  AssignUsersOptionsEnum,
  DeleteOrgsFormValuesType,
  DeleteSubOrgsOptionsEnum,
  IDeleteOrgsProps
} from '@components/organizations/types';
import { TrashOutlineIcon } from '@icons';

export const DELETE_MODAL_ID = 'delete-org-modal';
export const DELETE_SUB_ORGS_OPTION = 'delete-sub-orgs-option';
export const DELETE_MODAL_BUTTON_ID = 'delete-org-confirmation-button';
export const DELETE_ASSIGN_USERS_OPTION = 'delete-assign-users-option';
export const DELETE_MODAL_CONFIRMATION_ID = 'delete-org-confirmation-modal';
export const DELETE_MODAL_CONFIRMATION_INPUT_ID = 'delete-org-confirmation-input';
export const DELETE_MODAL_CONFIRMATION_ERROR_ID = 'delete-org-confirmation-error';

export const DELETE_ORGS_DEFAULT_VALUES: DeleteOrgsFormValuesType = {
  deleteSubOrgs: DeleteSubOrgsOptionsEnum.DELETE_SUB_ORGS,
  assignUsers: AssignUsersOptionsEnum.DONT_ASSIGN_USERS,
  organization: ''
};

export const DeleteOrgsModalConfirmation = ({ isOpen, onClose, handleSubmit }: IDeleteOrgsProps) => {
  const { t } = useTranslation(['organizations', 'common']);
  const [confirmDelete, setConfirmDelete] = useState<string>('');
  const [error, setError] = useState<boolean>(false);

  const onHandleValidation = useCallback(() => {
    if (confirmDelete !== t('deleteOrg').toUpperCase()) {
      setError(true);
    } else {
      setError(false);
      handleSubmit?.();
    }
  }, [confirmDelete, handleSubmit, t]);

  return (
    <InfoModal
      footer={
        <>
          <Button
            mr={2}
            variant="outline"
            onClick={() => {
              setConfirmDelete('');
              onClose();
            }}
          >
            {t('buttons.cancel', { ns: 'common' })}
          </Button>
          <Button colorPalette="crimsonRed" mr={2} onClick={onHandleValidation}>
            {t<string>('deleteOrg').toUpperCase()}
          </Button>
        </>
      }
      header={t('deleteOrgModal.title')}
      isOpen={isOpen}
      size={{ base: 'sm', lg: 'md' }}
      onClose={onClose}
    >
      <Flex data-testid={DELETE_MODAL_CONFIRMATION_ID} flexDir="column">
        <Text>{t('deleteOrgModal.TypeDeleteToConfirm')}</Text>
        <Input
          data-testid={DELETE_MODAL_CONFIRMATION_INPUT_ID}
          mb={1}
          mt={3}
          placeholder={t<string>('deleteOrg').toUpperCase()}
          onChange={e => {
            setError(false);
            setConfirmDelete(e.target.value);
          }}
        />
        {error && (
          <Text color="red" data-testid={DELETE_MODAL_CONFIRMATION_ERROR_ID} fontSize="sm">
            {t('deleteOrgModal.helperToDelete')}
          </Text>
        )}
      </Flex>
    </InfoModal>
  );
};

export const DeleteOrganization = ({ isOpen, onClose }: IDeleteOrgsProps) => {
  const { t } = useTranslation(['organizations', 'common']);
  const [isOpenConfirmation, { on: onOpenConfirmation, off: onCloseConfirmation }] = useBoolean();
  const {
    isSubOrg,
    hasSubOrgs,
    hasUsers,
    deleteOrgsOptions,
    assignUsersOptions,
    isNotValidFields,
    setIsValidating,
    onSubmit,
    parentSiblingOrgsOptions,
    isLoading
  } = useDeleteOrgs();

  const form = useAppForm({
    defaultValues: DELETE_ORGS_DEFAULT_VALUES,
    onSubmit: async ({ value }) => await onSubmit(value)
  });

  const values = form.state.values;
  const isNotValid = !isNotValidFields(values);

  const closeModal = useCallback(() => {
    form.reset();
    onClose();
  }, [form, onClose]);

  const deleteOrg = useCallback(() => {
    setIsValidating(true);
    if (!isNotValid) onOpenConfirmation();
  }, [isNotValid, onOpenConfirmation, setIsValidating]);

  return (
    <FormWrapper handleSubmit={form.handleSubmit}>
      <InfoModal
        footer={
          <>
            <Button mr={2} variant="outline" onClick={closeModal}>
              {t('buttons.cancel', { ns: 'common' })}
            </Button>
            <Button data-testid={DELETE_MODAL_BUTTON_ID} onClick={deleteOrg}>
              <TrashOutlineIcon />
              {t('deleteOrg')}
            </Button>
          </>
        }
        header={t('deleteOrgModal.title')}
        isOpen={isOpen}
        size={{ base: 'md', lg: '3xl' }}
        onClose={onClose}
      >
        <Flex data-testid={DELETE_MODAL_ID} flexDir="column" gap={4}>
          <Text>{t('deleteOrgModal.body')}</Text>
          <form.AppField name="deleteSubOrgs">
            {field => {
              return (
                <BaseInput formControlProps={{ isRequired: true }} label={t('deleteOrgModal.orgWithSubOrgs')}>
                  <field.SelectField
                    data-testid={DELETE_SUB_ORGS_OPTION}
                    isDisabled={!isSubOrg || !hasSubOrgs}
                    options={deleteOrgsOptions}
                  />
                </BaseInput>
              );
            }}
          </form.AppField>
          <form.AppField name="assignUsers">
            {field => {
              return (
                <BaseInput formControlProps={{ isRequired: true }} label={t('deleteOrgModal.orgWithUser')}>
                  <field.SelectField
                    data-testid={DELETE_ASSIGN_USERS_OPTION}
                    isDisabled={!hasUsers || parentSiblingOrgsOptions?.length === 0 || isLoading}
                    options={assignUsersOptions}
                  />
                </BaseInput>
              );
            }}
          </form.AppField>
          {values?.assignUsers === AssignUsersOptionsEnum.SELECT_ORG_TO_TRANSFER && (
            <form.AppField name="organization">
              {field => {
                return (
                  <BaseInput formControlProps={{ isRequired: true }} label={t('deleteOrgModal.orgWithUser')}>
                    <field.SelectField
                      isDisabled={!hasUsers}
                      isLoading={parentSiblingOrgsOptions?.length === 0 || isLoading}
                      options={parentSiblingOrgsOptions}
                    />
                  </BaseInput>
                );
              }}
            </form.AppField>
          )}
        </Flex>
      </InfoModal>
      <DeleteOrgsModalConfirmation
        handleSubmit={form.handleSubmit}
        isOpen={isOpenConfirmation}
        onClose={onCloseConfirmation}
      />
    </FormWrapper>
  );
};
