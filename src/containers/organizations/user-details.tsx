import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { IconAvatar } from '@linc-inc/yw-ui/components/avatars';
import { useBreakpointValue } from '@linc-inc/yw-ui/hooks/common';
import { Box, Button, Flex, Heading, Text } from '@linc-inc/yw-ui/ui';
import { OrganizationUserType, useDeleteOrganizationUserMutation } from '@reducers/organization-user';
import { toolsApi } from '@reducers/tools';
import { useGetUserDetailsQuery } from '@reducers/user';
import { useSuccessToast } from '@hooks/toasts';
import { ErrorAlert } from '@common/alerts';
import { ActionButton } from '@common/icon-buttons';
import { DeleteConfirmationModal } from '@common/modals';
import { ArrowBackIcon, TrashOutlineIcon, UserIcon } from '@icons';
import { useNavigateBack } from '@helpers/hooks/use-custom-navigate';
import { useSubmitMutation } from '@helpers/hooks/use-submit-mutation';
import PageLayout from '@layouts/page-layout';
import { DEFAULT_INPUT_SIZES, LIGHT_HIGH_EMPHASIS } from '@constants';

export const USER_DETAIL_HEADING_ID = 'user-detail-heading-id';
export const REMOVE_USER_ID = 'remove-user-id';

const DataBox = ({ label, value }: { label: string; value: string }) => (
  <Flex flexDir="column" gap={3}>
    <Text fontSize={DEFAULT_INPUT_SIZES}>{label}</Text>
    <Text color="text-low-emphasis" fontSize={DEFAULT_INPUT_SIZES}>
      {value}
    </Text>
  </Flex>
);

const UserDetails = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const isMobile = useBreakpointValue({ base: true, md: false });
  const { orgId = '', userId = '' } = useParams();
  const { currentData } = useGetUserDetailsQuery(userId, { skip: !userId });
  const navigateBack = useNavigateBack();
  const {
    firstName = '',
    lastName = '',
    email = '',
    jobDescription = '',
    jobTitle = '',
    organizations = []
  } = currentData ?? {};
  const deletionToast = useSuccessToast('organizationUsersServiceDeletionSuccess');
  const { organization: org } = organizations.find((org: OrganizationUserType) => org.organization.id === orgId) ?? {};
  const [mutation] = useDeleteOrganizationUserMutation();
  const removeUser = useSubmitMutation({ mutation });

  const deletionSuccessCallback = useCallback(() => {
    deletionToast();
    navigateBack();
    dispatch(toolsApi.util.resetApiState());
  }, [deletionToast, dispatch, navigateBack]);

  const deleteOrganizationUser = useCallback(
    (orgId: string, userId: string) =>
      removeUser({
        payload: { orgId, userId },
        successCallback: deletionSuccessCallback
      }),
    [deletionSuccessCallback, removeUser]
  );

  return (
    <PageLayout>
      <Flex
        bgColor="white500-gray800"
        borderRadius="lg"
        flexDir="column"
        gap={3}
        justifyContent="space-between"
        mb={8}
        px={{ base: 4, md: 8 }}
        py={4}
        w={{ base: '100%', md: '564px' }}
      >
        <Flex alignItems="center" flexDir="row" gap={3} mb={3} w="100%">
          <ActionButton
            aria-label={t('buttons.back', 'common')}
            color="text-high-emphasis"
            icon={ArrowBackIcon}
            mr={3}
            size={6}
            onClick={navigateBack}
          />
          <Heading data-testid={USER_DETAIL_HEADING_ID} variant="h2" w="100%">
            {t('organizations:userDetails')}
          </Heading>
        </Flex>
        <Flex alignItems="center" gap={4} mb={3}>
          <IconAvatar
            color={LIGHT_HIGH_EMPHASIS}
            icon={<UserIcon boxSize={{ base: '24px', lg: '34px' }} />}
            size={{ base: 'md', lg: 'lg' }}
          />
          <Heading variant="h4">{`${firstName} ${lastName}`}</Heading>
        </Flex>
        <Flex gap={4}>
          <DataBox label={t('fields.firstName')} value={firstName} />
          <DataBox label={t('fields.lastName')} value={lastName} />
        </Flex>
        <DataBox label={t('fields.email')} value={email} />
        <Flex flexDir={isMobile ? 'column' : 'row'} gap={4}>
          <DataBox label={t('fields.jobTitle')} value={jobTitle} />
          <DataBox label={t('fields.jobDescription')} value={jobDescription} />
        </Flex>
        <Box fontSize={DEFAULT_INPUT_SIZES}>
          <Text mb={3}>{t('organizations:organizations')}</Text>
          {organizations ? (
            organizations.map((orgUser: OrganizationUserType) => (
              <Text fontWeight="medium" key={orgUser.organization.id} textDecor="underline">
                {orgUser.organization.name}
              </Text>
            ))
          ) : (
            <Text>{t('organizations:none')}</Text>
          )}
        </Box>
        {org && (
          <DeleteConfirmationModal
            alert={
              org.keepContent ? undefined : (
                <ErrorAlert
                  subtle
                  alignItems="flex-start"
                  description={t<string>('organizations:removeUser.alertBody')}
                  mb={3}
                  title={t<string>('organizations:removeUser.alertTitle')}
                />
              )
            }
            body={t('organizations:removeUser.body')}
            callback={() => deleteOrganizationUser(org.id, userId)}
            header={t('organizations:removeUser.header')}
          >
            <Button
              colorPalette="crimsonRed"
              data-testid={REMOVE_USER_ID}
              mt={4}
              size="sm"
              variant="link"
              w="fit-content"
            >
              <TrashOutlineIcon size="sm" />
              {t('organizations:removeUser.header')}
            </Button>
          </DeleteConfirmationModal>
        )}
      </Flex>
    </PageLayout>
  );
};

export default UserDetails;
