import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import {
  Box,
  Button,
  DialogCloseTrigger,
  DialogContent,
  DialogDescription,
  DialogRoot,
  DialogTitle,
  Flex,
  Input
} from '@linc-inc/yw-ui/ui';
import { chooseFlexible } from '@linc-inc/yw-ui/utils/return-save-data';
import { filesApi, useDeleteFileMutation } from '@reducers/files/files-api';
import { LoadingSpinner } from '@common/loaders';
import { useSubmitMutation } from '@helpers/hooks/use-submit-mutation';
import { toast } from '@helpers/toast';
import { ALERT_STATUS } from '@typings/alert-status';

export const DELETE_FILE_DIALOG_ID = 'delete-file-dialog-id';
export const DELETE_FILE_INPUT_TEST_ID = 'delete-file-input-test-id';
export const DELETE_FILE_CONFIRM_BUTTON_TEST_ID = 'delete-file-confirm-button-test-id';
export const CardDocFileDelete = ({ id, isOpen, onClose }: { id: string; isOpen: boolean; onClose: () => void }) => {
  const { t } = useTranslation(['content', 'common']);
  const dispatch = useDispatch();
  const [inputValue, setInputValue] = useState('');
  const [mutation, { isLoading }] = useDeleteFileMutation();
  const submitDeleteFile = useSubmitMutation({ mutation });
  const confirmationValue = t<string>('generic.deleteConfirmationValue', { ns: 'common' });
  const loadingState = chooseFlexible(
    isLoading,
    <Box h="100%" left="0" pos="absolute" top="0" w="100%" zIndex="1000">
      <LoadingSpinner />
    </Box>,
    null
  );

  const handleDeleteFile = () => {
    if (inputValue === confirmationValue) {
      submitDeleteFile({
        payload: id,
        successCallback: () => {
          toast({
            title: t<string>('cardDocFileActions.delete.success', { ns: 'content' }),
            status: ALERT_STATUS.SUCCESS
          });
          dispatch(filesApi.util.invalidateTags(['FileList']));
          onClose();
        },
        errorCallback: () => {
          toast({
            title: t<string>('cardDocFileActions.delete.error', { ns: 'content' }),
            status: ALERT_STATUS.ERROR
          });
        }
      });
    }
  };

  return (
    <DialogRoot open={isOpen} placement="center" onInteractOutside={onClose}>
      <DialogContent p={4}>
        {loadingState}
        <DialogTitle fontSize="1.125rem" fontWeight="500">
          {t('cardDocFileActions.delete.title')}
        </DialogTitle>
        <DialogCloseTrigger onClick={onClose} />
        <DialogDescription fontSize="0.875rem" fontWeight="400" mt={4}>
          {t('cardDocFileActions.delete.description')}
          <Flex alignItems="center" data-testid={DELETE_FILE_DIALOG_ID} flexDir="row" gap={2} mt={4} w="100%">
            <Input
              data-testid={DELETE_FILE_INPUT_TEST_ID}
              placeholder={t<string>('fields.startTypingHere', { ns: 'common' })}
              type="text"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
            />
            <Button
              colorPalette="crimsonRed"
              data-testid={DELETE_FILE_CONFIRM_BUTTON_TEST_ID}
              disabled={inputValue !== confirmationValue}
              onClick={handleDeleteFile}
            >
              {t('buttons.confirm', { ns: 'common' })}
            </Button>
          </Flex>
        </DialogDescription>
      </DialogContent>
    </DialogRoot>
  );
};
