import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FiEdit } from 'react-icons/fi';
import { useDispatch } from 'react-redux';
import { useBoolean } from '@linc-inc/yw-ui/hooks/common';
import { Box, Button, Flex, IconButton, MenuContent, MenuItem, MenuRoot, MenuTrigger } from '@linc-inc/yw-ui/ui';
import { chooseFlexible } from '@linc-inc/yw-ui/utils/return-save-data';
import { ContentType, ContentTypeEnum, setShrinkContent, useDeleteContentMutation } from '@reducers/content';
import { ACTION_BUTTON_TEST_ID } from '@common/modals/delete-confirmation-modal';
import { ConfirmationModal } from '@components/common/modals';
import { ContentPreviewCell } from '@components/content/content-preview-cell';
import { EyeIcon, KebabVerticalIcon, TrashOutlineIcon } from '@icons';
import { useSuccessToast } from '@helpers/hooks/toasts';
import { useCustomNavigate } from '@helpers/hooks/use-custom-navigate';
import { useSubmitMutation } from '@helpers/hooks/use-submit-mutation';
import { CONTENT_PATH } from '@routes/paths';

export enum QuickActionsEnum {
  EDIT = 'edit',
  PREVIEW = 'preview',
  DELETE = 'delete'
}

export const CONTENT_PREVIEW_BUTTON_ID = 'content-preview-button-id';
export const CONTENT_ACTIONS_MENU_BTN_ID = 'content-action-menu-btn-id';
export const CONTENT_ACTIONS_MENU = 'content-action-menu';

export const DeleteContentAction = ({
  content,
  isOpen,
  onClose
}: {
  content: ContentType;
  isOpen: boolean;
  onClose: () => void;
}) => {
  const { id, name, linkContentCount } = content;
  const dispatch = useDispatch();
  const { t } = useTranslation('content');
  const [mutation] = useDeleteContentMutation();
  const onSubmit = useSubmitMutation({ mutation });
  const onSuccess = useSuccessToast('contentServiceDeletionSuccess');
  const firstBody = chooseFlexible(linkContentCount > 0, t('deleteWithLinkedContentFirst'), t('deleteBodyFirst'));
  const secondBody = chooseFlexible(linkContentCount > 0, t('deleteWithLinkedContentSecond'), t('deleteBodySecond'));
  const bodyMessage = `${firstBody} ${name} ${secondBody}`;

  const onDelete = useCallback(() => {
    onSubmit({ payload: id });
    onSuccess();
    dispatch(setShrinkContent(true));
  }, [onSubmit, onSuccess, id, dispatch]);

  return (
    <ConfirmationModal body={bodyMessage} header={t('deleteTitle')} isOpen={isOpen} onClose={onClose}>
      <Button
        colorPalette="crimsonRed"
        data-testid={`${ACTION_BUTTON_TEST_ID}-${id}`}
        size={{ base: 'sm', md: 'md' }}
        onClick={onDelete}
      >
        {t('buttons.delete', { ns: 'common' })}
      </Button>
    </ConfirmationModal>
  );
};

export const ContentActionCell = (content: ContentType) => {
  const { id } = content;
  const { t } = useTranslation('content');
  const path = `/${CONTENT_PATH}/${id}/edit`;
  const navigateToEdit = useCustomNavigate(path);
  const isSlidesContent = content.contentType === ContentTypeEnum.SLIDES;
  const [isOpenPreview, { on: onOpenPreview, off: onClosePreview }] = useBoolean();
  const [isOpenDelete, { on: onOpenDelete, off: onCloseDelete }] = useBoolean();

  const quickActions = [
    {
      label: t('generic.edit', { ns: 'common' }),
      icon: <FiEdit size={16} />,
      value: QuickActionsEnum.EDIT,
      color: 'trustworthyBlue.500',
      testId: `${CONTENT_ACTIONS_MENU}-${QuickActionsEnum.EDIT}`
    },
    {
      label: t('contentTable.preview', { ns: 'content' }),
      icon: <EyeIcon boxSize={4} />,
      value: QuickActionsEnum.PREVIEW,
      color: 'trustworthyBlue.500',
      testId: `${CONTENT_ACTIONS_MENU}-${QuickActionsEnum.PREVIEW}`
    },
    {
      label: t('buttons.delete', { ns: 'common' }),
      icon: <TrashOutlineIcon boxSize={4} />,
      value: QuickActionsEnum.DELETE,
      color: 'red.500',
      testId: `${CONTENT_ACTIONS_MENU}-${QuickActionsEnum.DELETE}`
    }
  ];

  const filteredQuickActions = quickActions.filter(action =>
    chooseFlexible(action.value === QuickActionsEnum.PREVIEW && isSlidesContent, false, true)
  );

  const mappingActions = {
    [QuickActionsEnum.EDIT]: {
      onClick: () => {
        navigateToEdit();
      }
    },
    [QuickActionsEnum.PREVIEW]: {
      onClick: () => {
        onOpenPreview();
      }
    },
    [QuickActionsEnum.DELETE]: {
      onClick: () => {
        onOpenDelete();
      }
    }
  };

  const handleAction = (action: QuickActionsEnum) => {
    mappingActions[action]?.onClick();
  };

  return (
    <>
      <MenuRoot closeOnSelect>
        <MenuTrigger asChild>
          <IconButton
            colorPalette="relatableGray"
            data-testid={`${CONTENT_ACTIONS_MENU_BTN_ID}-${id}`}
            size="xs"
            variant="ghost"
          >
            <KebabVerticalIcon />
          </IconButton>
        </MenuTrigger>
        <MenuContent display="flex" flexDir="column">
          {filteredQuickActions.map(({ value, color, testId, icon, label }) => (
            <MenuItem key={value} value={value} onClick={() => handleAction(value as QuickActionsEnum)}>
              <Flex alignItems="center" color={color} data-testid={testId}>
                <Box mr={2}>{icon}</Box>
                {label}
              </Flex>
            </MenuItem>
          ))}
        </MenuContent>
      </MenuRoot>
      <DeleteContentAction content={content} isOpen={isOpenDelete} onClose={onCloseDelete} />
      <ContentPreviewCell {...content} isOpen={isOpenPreview} onClose={onClosePreview} />
    </>
  );
};
