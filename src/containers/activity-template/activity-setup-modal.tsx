import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { OptionCard, SourceOptions, TopicOption } from '@linc-inc/yw-ui/components/source-options';
import {
  Box,
  Button,
  Center,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  Steps,
  StepsIndicator,
  StepsItem,
  StepsList,
  StepsSeparator,
  StepsTrigger,
  Text
} from '@linc-inc/yw-ui/ui';
import { chooseFlexible } from '@linc-inc/yw-ui/utils/return-save-data';
import { SelectInput } from '@common/inputs';
import { useActivityForm } from '@helpers/hooks/activity-template/use-activity-form';
import { LincCharacterThinkingAnimation } from '@assets/animations';
import { ActivitySetupProps, StepItemType } from './types';

const CLASS_OPTIONS = [
  { label: 'Class 1', value: 'class1' },
  { label: 'Class 2', value: 'class2' },
  { label: 'Class 3', value: 'class3' }
];

const TIME_OPTIONS = [
  { label: '5 mins', value: '5' },
  { label: '10 mins', value: '10' },
  { label: '15 mins', value: '15' },
  { label: '30 mins', value: '30' },
  { label: '45 mins', value: '45' },
  { label: '60 mins', value: '60' },
  { label: 'Other', value: 'other' }
];

export const ActivitySetupModal = ({ isOpen, onClose, onSubmit }: ActivitySetupProps) => {
  const { t } = useTranslation('activity-template');

  const [activeStep, setActiveStep] = useState(0);
  const { formState, isValid, reset } = useActivityForm();

  const steps = useMemo<StepItemType[]>(
    () => [
      {
        disabled: false,
        completed: activeStep > 0
      },
      {
        disabled: !isValid,
        completed: activeStep > 1
      },
      {
        disabled: !isValid || activeStep < 1,
        completed: false
      }
    ],
    [activeStep, isValid]
  );

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    } else {
      onClose();
    }
  }, [activeStep, onClose]);

  const handleContinue = useCallback(() => {
    if (activeStep < 2) {
      setActiveStep(activeStep + 1);
    } else {
      onSubmit?.(formState);
      reset();
      onClose();
    }
  }, [activeStep, formState, onSubmit, reset, onClose]);

  return (
    <Modal isCentered isOpen={isOpen} size="5xl" onClose={onClose}>
      <ModalContent m={0} maxW="57rem" p={0}>
        <Flex direction="row" minH="32rem">
          <Flex
            align="center"
            bg="smartBlue.50"
            borderBottomLeftRadius="md"
            borderTopLeftRadius="md"
            justify="center"
            p={6}
            w="40%"
          >
            <Box as="span" display="block" h="auto" w="100%">
              <LincCharacterThinkingAnimation />
            </Box>
          </Flex>
          <Flex direction="column" w="60%">
            <ModalHeader justifyContent="center" pb={2} pt={8} px={8} textAlign="right">
              <Text fontSize="1.5rem" fontWeight="bold">
                {t('title')}
              </Text>
            </ModalHeader>
            <Center px={8}>
              <Text color="gray.500" fontSize="1rem" textAlign="center">
                {t('description')}
              </Text>
            </Center>

            <ModalCloseButton />
            <ModalBody pb={0} pt={2} px={8}>
              <Flex direction="column" gap={6} mb={4} px={16} py={8}>
                <Steps>
                  <StepsList>
                    {steps.map((step, index) => (
                      <StepsItem index={index} key={`step-${index}`}>
                        <StepsTrigger disabled={step.disabled}>
                          <StepsIndicator
                            bg={chooseFlexible(step.completed, 'blue.500', 'white')}
                            borderColor={chooseFlexible(
                              step.completed,
                              'blue.500',
                              chooseFlexible(index === activeStep, 'blue.500', 'gray.200')
                            )}
                            color={chooseFlexible(
                              step.completed,
                              'white',
                              chooseFlexible(index === activeStep, 'blue.500', 'gray.500')
                            )}
                            opacity={chooseFlexible(step.disabled, 0.5, 1)}
                          >
                            {`${index + 1}`.padStart(2, '0')}
                          </StepsIndicator>
                        </StepsTrigger>
                        {index < steps.length - 1 && <StepsSeparator />}
                      </StepsItem>
                    ))}
                  </StepsList>
                </Steps>
              </Flex>
              <Flex gap={4}>
                <Box flex="1">
                  <SelectInput label={t('classSelector.label')} options={CLASS_OPTIONS} />
                </Box>
                <Box flex="1">
                  <SelectInput label={t('timeSelector.label', 'Select time')} options={TIME_OPTIONS} />
                </Box>
              </Flex>

              <SourceOptions initialSelectedOption="topic1" my={4} width="full">
                <TopicOption
                  optionKey="topic1"
                  placeholder={String(t('sourceOptions.objectivePlaceholder'))}
                  subtitle={String(t('sourceOptions.objectiveLabel'))}
                  title={String(t('sourceOptions.fromTopic'))}
                />

                <OptionCard
                  isDisabled
                  optionKey="comingSoon"
                  subtitle={String(t('sourceOptions.comingSoon'))}
                  title={String(t('sourceOptions.comingSoon'))}
                />
              </SourceOptions>
            </ModalBody>
            <ModalFooter gap={2} justifyContent="space-between" mt={4} pt={8} px={8}>
              <Button variant="outline" onClick={handleBack}>
                {chooseFlexible(activeStep === 0, t('buttons.back'), t('buttons.previous'))}
              </Button>
              <Button disabled={!isValid} variant="solid" onClick={handleContinue}>
                {chooseFlexible(activeStep === 2, t('buttons.create'), t('buttons.continue'))}
              </Button>
            </ModalFooter>
          </Flex>
        </Flex>
      </ModalContent>
    </Modal>
  );
};
