import { useCallback, useEffect } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useBreakpointValue } from '@linc-inc/yw-ui/hooks/common';
import { Button, IconButton } from '@linc-inc/yw-ui/ui';
import { togglePrompt, useGetContentByIdQuery } from '@reducers/content';
import { useContentPrompt, useContentStreamStatus } from '@hooks/selectors';
import { useContentAndVersionParams } from '@hooks/use-content-version-params';
import { EditOutlineIcon } from '@icons';
import { toast } from '@helpers/toast';
import { ALERT_STATUS } from '@typings';

export const CONTENT_REQUEST_CHANGES_BTN_ID = 'content-request-changes-btn-id';
const warningDuration = 20000;
type RequestChangesButtonType = {
  onClose: () => void;
  isRightPanel?: boolean;
  isProcessing?: boolean;
  isStreamError?: boolean;
};

const RequestChangesButton = ({
  onClose,
  isRightPanel = false,
  isProcessing = false,
  isStreamError = false
}: RequestChangesButtonType) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('content');
  const requestChangesLabel = t('requestChanges');
  const { contentId } = useContentAndVersionParams();
  const isDesktop = useBreakpointValue({ base: false, lg: true });
  const { data, isSuccess } = useGetContentByIdQuery(contentId, { skip: !contentId });

  const showPrompt = useContentPrompt();
  const { isContentStreamLoading } = useContentStreamStatus();
  const isDisabled = showPrompt || isContentStreamLoading || isProcessing || isStreamError || !isSuccess;

  const requestChanges = useCallback(() => {
    if (!data?.prompt)
      toast({
        duration: warningDuration,
        status: ALERT_STATUS.WARNING,
        description: (
          <Trans
            components={{
              br: <br />
            }}
            i18nKey="requestChangesWarning"
            ns="content"
          />
        )
      });
    dispatch(togglePrompt(true));
    if (!isRightPanel) onClose();
  }, [data?.prompt, dispatch, isRightPanel, onClose]);

  useEffect(() => {
    dispatch(togglePrompt(false));
  }, [dispatch]);

  if (isDesktop && isRightPanel) return null;

  if (isRightPanel)
    return (
      <Button data-testid={CONTENT_REQUEST_CHANGES_BTN_ID} disabled={isDisabled} ml={3} onClick={requestChanges}>
        {requestChangesLabel}
      </Button>
    );

  return (
    <IconButton
      aria-label={requestChangesLabel}
      colorPalette="crimsonRed"
      data-testid={CONTENT_REQUEST_CHANGES_BTN_ID}
      disabled={isDisabled}
      size="2xs"
      onClick={requestChanges}
    >
      <EditOutlineIcon size="sm" />
    </IconButton>
  );
};

export default RequestChangesButton;
