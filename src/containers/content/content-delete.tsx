import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuItem } from '@linc-inc/yw-ui/ui';
import { chooseFlexible } from '@linc-inc/yw-ui/utils/return-save-data';
import { useDeleteContentMutation } from '@reducers/content';
import { useContentStreamStatus } from '@hooks/selectors/content';
import { useCustomNavigate } from '@hooks/use-custom-navigate';
import { useSubmitMutation } from '@hooks/use-submit-mutation';
import { DeleteConfirmationModal } from '@common/modals';
import { TrashOutlineIcon } from '@icons';
import { toast } from '@helpers/toast';
import { CONTENT_PATH } from '@routes/paths';
import { MENU_ICON_SIZE } from '@constants';
import { ALERT_STATUS } from '@typings';

export const DELETE_CONTENT_MENU_ITEM_ID = 'delete-content-menu-item-id';

export const ContentDelete = ({
  contentId,
  contentName,
  hasLinkedContent
}: {
  contentId: string;
  contentName: string;
  hasLinkedContent?: boolean;
}) => {
  const { t } = useTranslation('content');
  const [mutation] = useDeleteContentMutation();
  const deleteContent = useSubmitMutation({ mutation });
  const navigateToContents = useCustomNavigate(`/${CONTENT_PATH}`);
  const { isContentStreamLoading } = useContentStreamStatus();
  const bodyMessage = chooseFlexible(hasLinkedContent, 'deleteWithLinkedContent', 'deleteBody');

  const handleDelete = useCallback(() => {
    deleteContent({
      payload: contentId,
      successCallback: () => {
        toast({
          status: ALERT_STATUS.SUCCESS,
          description: t<string>('contentServiceDeletionSuccess', { ns: 'success-messages' })
        });
        navigateToContents();
      }
    });
  }, [contentId, deleteContent, navigateToContents, t]);

  return (
    <DeleteConfirmationModal
      body={t(bodyMessage, { name: contentName })}
      callback={handleDelete}
      header={t('deleteTitle')}
    >
      <MenuItem
        color="crimsonRed.600"
        data-testid={DELETE_CONTENT_MENU_ITEM_ID}
        disabled={isContentStreamLoading}
        value="deleteContent"
      >
        <TrashOutlineIcon {...MENU_ICON_SIZE} />
        {t('buttons.deleteContent')}
      </MenuItem>
    </DeleteConfirmationModal>
  );
};
