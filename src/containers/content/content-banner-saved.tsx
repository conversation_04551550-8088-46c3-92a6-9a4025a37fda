import { Dispatch, SetStateAction, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Flex, Text } from '@linc-inc/yw-ui/ui';
import { setContentUpdateTab } from '@reducers/content';
import { CloseIcon, InfoOutlineIcon } from '@icons';

export const CONTENT_BANNER_SAVED_ID = 'content-banner-id';
export const VERSION_BANNER_BUTTON_ID = 'version-history-button-id';

type ContentBannerSavedPropsType = {
  setShowContentBannerSaved: Dispatch<SetStateAction<boolean>>;
};

export const ContentBannerSaved = ({ setShowContentBannerSaved }: ContentBannerSavedPropsType) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const handleOnCloseLabel = useCallback(() => {
    setShowContentBannerSaved(false);
    dispatch(setContentUpdateTab(false));
  }, [dispatch, setShowContentBannerSaved]);

  return (
    <Flex
      alignItems="center"
      bg="blue50-blue200"
      data-testid={CONTENT_BANNER_SAVED_ID}
      hideBelow="lg"
      justifyContent="space-between"
      minH="42px"
      px={4}
      py={3}
      w="100%"
    >
      <Flex alignItems="center" gap={2} justifyContent="space-between">
        <InfoOutlineIcon color="blue500-white500" />
        <Text color="blue500-white500" fontSize="sm" fontWeight="bold">
          {t<string>('content:updatedContent.title')}
        </Text>
        <Text fontSize="sm">{t<string>('content:updatedContent.message')}</Text>
      </Flex>
      <Flex
        color="text-high-emphasis"
        cursor="pointer"
        data-testid={VERSION_BANNER_BUTTON_ID}
        gap={1}
        onClick={handleOnCloseLabel}
      >
        <CloseIcon boxSize={2} />
      </Flex>
    </Flex>
  );
};
