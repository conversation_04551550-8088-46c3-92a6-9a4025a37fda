import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Button, Flex } from '@linc-inc/yw-ui/ui';
import { BeansStateEnum, setBeansNotification, setState } from '@reducers/beans';
import { setChatStreamStatus } from '@reducers/chat';
import { useStopStreamMutation } from '@reducers/stream';
import { StopIcon } from '@icons';
import { StreamStatusEnum } from '@typings';

export const STOP_STREAM_BUTTON_ID = 'stop-stream-button-id';

export const StopStream = ({ id, source }: { id: string; source: 'chat' | 'content' }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('common');
  const [stopStream] = useStopStreamMutation();

  const chatStopStream = useCallback(() => {
    stopStream(id);
    dispatch(setChatStreamStatus(StreamStatusEnum.COMPLETED));
  }, [dispatch, id, stopStream]);

  const contentStopStream = useCallback(() => {
    stopStream(id);
    dispatch(setState({ state: BeansStateEnum.NOTIFICATION, open: true }));
    dispatch(setBeansNotification({ message: t<string>('stopNotification', { ns: 'content' }) }));
  }, [dispatch, id, stopStream, t]);

  const handleStopStream = source === 'chat' ? chatStopStream : contentStopStream;

  return (
    <Flex bottom={0} justifyContent="center" my={2} position="absolute" w="100%">
      <Button background="white" data-testid={STOP_STREAM_BUTTON_ID} variant="outline" onClick={handleStopStream}>
        <StopIcon size="sm" />
        {t('buttons.stopGenerating')}
      </Button>
    </Flex>
  );
};
