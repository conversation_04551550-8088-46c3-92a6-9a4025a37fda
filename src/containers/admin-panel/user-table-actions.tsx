import { useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { MdOutlineEmojiFlags, MdOutlinePassword } from 'react-icons/md';
import { useDispatch } from 'react-redux';
import { Tooltip } from '@chakra-ui/react';
import { Button, HStack, IconButton, MenuContent, MenuItem, MenuRoot, MenuTrigger } from '@linc-inc/yw-ui/ui';
import {
  useDeleteUserMutation,
  useImpersonateMutation,
  useUpdateUserMutation,
  useUpdateUserPasswordMutation
} from '@reducers/admin-panel';
import { userApi } from '@reducers/user';
import { camelize } from '@hooks/select-options';
import { selectUser } from '@hooks/selectors';
import { useErrorToast, useSuccessToast } from '@hooks/toasts';
import { copy } from '@hooks/use-copy-to-clipboard';
import { useCustomNavigate } from '@hooks/use-custom-navigate';
import { useSubmitMutation } from '@hooks/use-submit-mutation';
import { StrictDeleteModal, StrictDeleteModalExposedFns } from '@common/modals';
import { CheckIcon, EditOutlineIcon, ImpersonateIcon, TrashOutlineIcon } from '@icons';
import { generatePassword } from '@utils/password-generator';
import { toast } from '@helpers/toast';
import { ADMIN_PANEL_PATH, HOME_PATH } from '@routes/paths';
import { ALERT_STATUS, PermissionsFlags, UserType } from '@typings';

export const USER_EDIT_BUTTON_ID = 'user-edit-button-id';
export const USER_DELETE_BUTTON_ID = 'user-delete-button-id';
export const USER_PERMISSION_FLAG_MENU_ID = 'user-permission-flag-menu-id';
export const USER_CHANGE_PASSWORD_BUTTON_ID = 'user-change-password-button-id';
export const USER_IMPERSONATES_BUTTON_ID = 'user-impersonates-button-id';

export const UserPermissionFlags = ({ userId, permissions }: { userId: string; permissions: PermissionsFlags[] }) => {
  const { t } = useTranslation('admin-panel');
  const [update, { isLoading }] = useUpdateUserMutation({ fixedCacheKey: userId });
  const updateUser = useSubmitMutation({ mutation: update });

  const setFlags = useCallback(
    (flag: PermissionsFlags, isRemove = false) => {
      let newPermissions = [...permissions.filter(permission => Object.values(PermissionsFlags).includes(permission))];
      if (isRemove) newPermissions = newPermissions.filter(permission => permission !== flag);
      else if (!isRemove && !newPermissions.includes(flag)) newPermissions.push(flag);
      updateUser({ payload: { userId, permissions: newPermissions } });
    },
    [permissions, updateUser, userId]
  );

  return (
    <MenuRoot>
      <Tooltip closeOnClick shouldWrapChildren label={t('fields.permissions', { ns: 'organizations' })}>
        <MenuTrigger asChild>
          <IconButton
            aria-label={t<string>('fields.permissions', { ns: 'organizations' })}
            data-testid={`${USER_PERMISSION_FLAG_MENU_ID}-${userId}`}
            disabled={isLoading}
            loading={isLoading}
            size="xs"
            variant="ghost"
          >
            <MdOutlineEmojiFlags />
          </IconButton>
        </MenuTrigger>
      </Tooltip>
      <MenuContent display="flex" flexDir="column" minW="156px" w="fit-content">
        {Object.values(PermissionsFlags).map(flag => {
          const isChecked = permissions.includes(flag);

          return (
            <MenuItem
              asChild
              cursor="pointer"
              data-testid={`${USER_PERMISSION_FLAG_MENU_ID}-${flag}-${userId}`}
              key={flag}
              value={flag}
              onClick={() => setFlags(flag, isChecked)}
            >
              <Button variant="ghost">
                {permissions.includes(flag) && <CheckIcon size="sm" />}
                {t(`userFlags.${camelize(flag)}`)}
              </Button>
            </MenuItem>
          );
        })}
      </MenuContent>
    </MenuRoot>
  );
};

export const UserTableActions = ({ id: userId }: UserType) => {
  const { id } = selectUser();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const ref = useRef<StrictDeleteModalExposedFns>(null);
  const [mutation] = useDeleteUserMutation();
  const onDelete = useSubmitMutation({ mutation });
  const deleteToast = useSuccessToast('userDeletionSuccess');
  const customErrorToast = useErrorToast('unableToDeleteAccount');
  const [passwordMutation] = useUpdateUserPasswordMutation();
  const onUpdatePassword = useSubmitMutation({ mutation: passwordMutation });
  const navigateToForm = useCustomNavigate(`/${ADMIN_PANEL_PATH}/users/${userId}`);
  const [, { isLoading }] = useUpdateUserMutation({ fixedCacheKey: userId });
  const [impersonateMutation] = useImpersonateMutation();
  const onImpersonate = useSubmitMutation({ mutation: impersonateMutation });

  const navigateHome = useCustomNavigate(`/${HOME_PATH}`);
  const deleteUser = useCallback(() => {
    onDelete({ payload: userId, successCallback: deleteToast, customErrorToast });
  }, [customErrorToast, deleteToast, onDelete, userId]);

  const updateUserPassword = useCallback(() => {
    const password = generatePassword();

    onUpdatePassword({
      payload: { userId, password, passwordConfirmation: password },
      successCallback: () => {
        copy(password);
        toast({
          title: '',
          id: 'copyClipboard',
          status: ALERT_STATUS.SUCCESS,
          description: t<string>('generic.copyClipboard')
        });
      }
    });
  }, [onUpdatePassword, t, userId]);

  const handleImpersonate = useCallback(() => {
    onImpersonate({
      payload: userId,
      successCallback: () => {
        dispatch(userApi.util.invalidateTags(['User']));
        navigateHome();
      }
    });
  }, [onImpersonate, navigateHome, userId, dispatch]);

  return (
    <>
      <HStack>
        <IconButton
          aria-label={t<string>('generic.delete')}
          data-testid={`${USER_IMPERSONATES_BUTTON_ID}-${userId}`}
          disabled={id === userId || isLoading}
          variant="ghost"
          onClick={handleImpersonate}
        >
          <ImpersonateIcon boxSize={4} />
        </IconButton>
        <Tooltip closeOnClick shouldWrapChildren label={t('buttons.changePassword')}>
          <IconButton
            aria-label={t<string>('fields.password')}
            data-testid={`${USER_CHANGE_PASSWORD_BUTTON_ID}-${userId}`}
            disabled={isLoading}
            variant="ghost"
            onClick={updateUserPassword}
          >
            <MdOutlinePassword height="1rem" width="1rem" />
          </IconButton>
        </Tooltip>
        {/* <UserPermissionFlags userId={userId} permissions={[]} /> */}
        <IconButton
          aria-label={t<string>('generic.edit')}
          data-testid={`${USER_EDIT_BUTTON_ID}-${userId}`}
          disabled={isLoading}
          variant="ghost"
          onClick={navigateToForm}
        >
          <EditOutlineIcon boxSize={4} />
        </IconButton>
        <IconButton
          aria-label={t<string>('generic.delete')}
          colorScheme="crimsonRed"
          data-testid={`${USER_DELETE_BUTTON_ID}-${userId}`}
          disabled={id === userId || isLoading}
          variant="ghost"
          onClick={() => ref.current?.onOpen()}
        >
          <TrashOutlineIcon boxSize={4} />
        </IconButton>
      </HStack>
      <StrictDeleteModal
        body={t('deleteUserConfirmationBody', { ns: 'admin-panel' })}
        callback={deleteUser}
        header={t('account.deleteAccount')}
        ref={ref}
      />
    </>
  );
};
