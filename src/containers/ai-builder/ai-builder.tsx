import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { BlockerFunction } from 'react-router-dom';
import { DetailsSliceType, resetAiBuilder } from '@reducers/ai-builder';
import { useSteps } from '@hooks/ai-builder';
import { useAiBuilderStepData, useCurrentAiBuilderStep, useIsNoMenuScreen } from '@hooks/selectors/ai-builder';
import { useHandleToolAccess } from '@hooks/tools';
import { useOnLeave } from '@hooks/use-on-leave';
import { AiBuilderFloatingBar } from '@components/ai-builder';
import { BeforeLeaveConfirmation } from '@containers/global';
import AiContainer from './ai-container';

const AiBuilder = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { tool, accessDenied } = useHandleToolAccess(true);
  const { steps, titles, summaryTitle = '', stepStrings } = useSteps(tool?.name ?? '');
  const currentStep = useCurrentAiBuilderStep();
  const isNoMenuScreen = useIsNoMenuScreen();
  const currentStepName = stepStrings[Number(currentStep)];
  const { isAutoBuilt } = useAiBuilderStepData(currentStepName) as DetailsSliceType;

  const resetStep = useCallback(() => dispatch(resetAiBuilder()), [dispatch]);
  useOnLeave(resetStep);

  /* istanbul ignore next */
  const blockerFn: BlockerFunction = useCallback(() => isNoMenuScreen, [isNoMenuScreen]);

  if (accessDenied) return null;

  return (
    <>
      <AiContainer
        floatingBar={<AiBuilderFloatingBar />}
        heading={titles[Number(currentStep)]}
        isAutoBuilt={isAutoBuilt}
        summaryTitle={summaryTitle}
        toolId={tool?.id ?? ''}
      >
        {steps[Number(currentStep)]}
      </AiContainer>
      {isNoMenuScreen && (
        <BeforeLeaveConfirmation blockerFn={blockerFn} body={t('beforeLeaveContent', { ns: 'common' })} />
      )}
    </>
  );
};

export default AiBuilder;
