/* istanbul ignore file */
import React, { useCallback, useRef, useState } from 'react';
import { IoCloseOutline } from 'react-icons/io5';
import { useParams } from 'react-router-dom';
import { EditorContent, EditorEvents, useEditor } from '@tiptap/react';
import { Box, Button, Flex, HStack, IconButton, Skeleton, Stack, Text, VStack } from '@linc-inc/yw-ui/ui';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import { TextAreaInput } from '@components/common/inputs';
import { NewFileButton } from '@components/common/inputs/file-input/new-file-button';
import { EditorToolbar, TEST_EXTENSIONS } from '@components/editor';
import { AiStarsIcon, TrashOutlineIcon } from '@icons';
import { GeneratedContentContext } from '@helpers/hooks/editor/editor-context';
import { BASE_URL } from '@constants';
import '@components/editor/styles/math-styles.css';
import '@components/editor/styles/general-styles.css';

// Local style variables
const buttonStyle: React.CSSProperties = {
  position: 'fixed',
  top: '10%',
  right: '10%',
  zIndex: 20
};

const modalStyle: React.CSSProperties = {
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 20
};

const modalContentStyle: React.CSSProperties = {
  background: 'white',
  minHeight: '50vh',
  padding: '20px',
  borderRadius: '5px',
  width: '90%'
};

const editorContainerStyle: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  width: '100%'
};

const EDITOR_STYLE: React.CSSProperties = {
  width: '100%',
  paddingTop: 6,
  flexDirection: 'column',
  maxHeight: '70vh',
  paddingLeft: '85px',
  paddingRight: '85px',
  paddingBottom: 'calc(50vhpx)',
  overflowY: 'auto'
};

const TESTING_FEATURE_FLAG = 'templates_prototype_testing';

export const ContentTemplateProt: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const isAllowedTestingTemplates = useFeatureFlagEnabled(TESTING_FEATURE_FLAG);
  const [file, setFile] = useState<File | null>(null);
  const [context, setContext] = useState<string>('');
  const [updatedContent, setUpdatedContent] = useState<string>('');
  const containerRef = useRef<HTMLDivElement>(null);
  const fileRef = useRef<HTMLInputElement>(null);

  // Using custom useMutation hook
  const {
    mutate: saveTemplate,
    isLoading: isSaving,
    error: saveError,
    data: templateHTML,
    reset: resetTemplate
  } = useMutation<FormData, { translationKey: string; message: string }>('template', postCall);
  const {
    data: filledTemplate,
    mutate: fillTemplate,
    isLoading: isFilling,
    error: fillError,
    reset: resetFilledTemplate
  } = useMutation<string, { translationKey: string; message: string }>('template/fill-template', postCall);
  const { mutate: deleteTemplate, isLoading: isDeleting } = useMutation<
    undefined,
    { translationKey: string; message: string }
  >('template/delete', deleteCall);

  const editor = useEditor(
    {
      extensions: TEST_EXTENSIONS,
      onCreate({ editor }: EditorEvents['create']) {
        if (!editor || editor.isEmpty) return;
        const currentState = editor.getHTML();
        setUpdatedContent(currentState);
      },
      onUpdate({ editor }: EditorEvents['update']) {
        if (!editor || editor.isEmpty) return;
        const currentState = editor.getHTML();
        setUpdatedContent(currentState);
      },
      content: filledTemplate?.message ?? templateHTML?.message ?? ''
    },
    [templateHTML, filledTemplate]
  );

  const handleDelete = useCallback(() => {
    deleteTemplate(undefined);
  }, [deleteTemplate]);

  if (!isAllowedTestingTemplates) return null;

  const handleFileChange = () => {
    const uploadedFile = fileRef.current?.files?.[0];
    if (
      uploadedFile &&
      uploadedFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ) {
      setFile(uploadedFile);
      // add a valid formData using the file to be sent to saveTemplate
      const formData = new FormData();
      formData.append('file', uploadedFile);
      saveTemplate(formData);
    } else {
      alert('Please upload a valid .docx file.');
    }
  };

  const handleFill = () => {
    if (updatedContent) {
      fillTemplate(JSON.stringify({ context, template: updatedContent }));
    }
  };

  const resetState = (closeModal = false) => {
    setIsModalOpen(closeModal);
    setFile(null);
    setContext('');
    setUpdatedContent('');
    resetFilledTemplate();
    resetTemplate();
    handleDelete();
    if (fileRef.current) {
      fileRef.current.value = '';
    }
  };
  const templateReady = Boolean(templateHTML?.message);

  return (
    <div>
      <Button style={buttonStyle} onClick={() => setIsModalOpen(true)}>
        Template Testing
      </Button>

      {isModalOpen && (
        <Flex style={modalStyle}>
          <Flex flexDir="row-reverse" justifyContent="space-between" overflowX="hidden" style={modalContentStyle}>
            {isSaving ? (
              <Stack gap="6" width="100%">
                <HStack width="100%">
                  <Skeleton height="100px" w="100px" />
                  <Skeleton height="100px" w="100%" />
                </HStack>
                <Skeleton height="400px" />
              </Stack>
            ) : (
              <>
                <IconButton aria-label="close" variant="ghost" onClick={() => resetState(false)}>
                  <IoCloseOutline />
                </IconButton>
                <VStack gap={8} width="98%">
                  <HStack alignSelf="self-start" gap={8} justifyContent="space-evenly" p={4} w="100%">
                    <Box>
                      <NewFileButton acceptedFilesExt={['docx']} fileInputRef={fileRef} onChange={handleFileChange} />
                      <Box>{saveError && <Text color="red">{saveError}</Text>}</Box>
                    </Box>
                    <Flex alignItems="center" flex={1} flexDir="row" gap={4} justifyContent="center">
                      <TextAreaInput
                        label="Template Context"
                        textAreaProps={{
                          value: context,
                          isDisabled: Boolean(!file?.name),
                          onChange: e => setContext(e.target.value),
                          placeholder: 'Your context here...',
                          maxLength: 5000
                        }}
                      />

                      {fillError && <Text color="red">{fillError}</Text>}
                      <VStack flex={2}>
                        <Button disabled={!templateReady} loading={isFilling} w="100%" onClick={handleFill}>
                          <AiStarsIcon size="sm" />
                          Fill Template
                        </Button>
                        <Button
                          colorPalette="crimsonRed"
                          disabled={!templateReady}
                          loading={isDeleting}
                          variant="outline"
                          w="100%"
                          onClick={() => resetState(true)}
                        >
                          <TrashOutlineIcon size="sm" />
                          Delete Data
                        </Button>
                      </VStack>
                    </Flex>
                  </HStack>
                  <VStack flex={1} w="100%">
                    {/* Editor Component Placeholder */}
                    {templateHTML?.message ? (
                      <div style={editorContainerStyle}>
                        {isFilling ? (
                          <Skeleton height="400px" />
                        ) : (
                          <GeneratedContentContext.Provider
                            value={{
                              prompts: [],
                              editor,
                              containerRef,
                              isReadonly: false
                            }}
                          >
                            <EditorToolbar />
                            <div ref={containerRef} style={EDITOR_STYLE}>
                              <EditorContent editor={editor} />
                            </div>
                          </GeneratedContentContext.Provider>
                        )}
                      </div>
                    ) : (
                      <Text>upload a template first</Text>
                    )}
                  </VStack>
                </VStack>
              </>
            )}
          </Flex>
        </Flex>
      )}
    </div>
  );
};

const TEMPLATE_API = BASE_URL + '/tool';

// Custom useMutation Hook
const useMutation = <T, R>(method: string, mutationFn: (method: string, data: T) => Promise<R>) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<R | null>(null);
  const { toolId = '' } = useParams();

  const mutate = async (data: T) => {
    if (!toolId) return;
    setIsLoading(true);
    setError(null);
    setData(null);
    try {
      const result = await mutationFn(`${toolId}/${method}`, data);
      setData(result);

      return result;
    } catch {
      setError('check network for errors');
    } finally {
      setIsLoading(false);
    }
  };

  return { mutate, isLoading, error, data, reset: () => setData(null) };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function postCall<R>(method: string, data: any): Promise<R> {
  const headers = new Headers();
  if (typeof data === 'string') headers.append('Content-Type', 'application/json');
  const response = await fetch(`${TEMPLATE_API}/${method}`, {
    headers,
    method: 'POST',
    body: data
  });
  if (!response.ok) {
    throw new Error('Failed to process template');
  }

  return response.json();
}

async function deleteCall<R>(method: string): Promise<R> {
  const response = await fetch(`${TEMPLATE_API}/${method}`, {
    method: 'DELETE'
  });
  if (!response.ok) {
    throw new Error('Failed to delete template');
  }

  return response.json();
}
