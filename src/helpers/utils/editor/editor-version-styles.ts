import { ContainerProps, FlexProps } from '@ui/layout';
import { ButtonType } from '@common/buttons';

const EDITOR_SPACING = 16;
const EDITOR_SPACING_MOBILE = 12;
const EDITOR_BORDER_RADIUS = 8;

export type EditorVersionStylesType = {
  parentContainerStyles: ContainerProps;
  contentContainerStyles: FlexProps;
  panelContainerStyles: FlexProps;
  containerStyles: FlexProps;
};

export const getEditorVersionStyles = (): EditorVersionStylesType => {
  const parentContainerStyles: ContainerProps = {
    display: 'flex',
    flexDir: 'column',
    w: '100%',
    overflow: 'hidden',
    css: {
      '@media print': {
        position: 'unset',
        overflow: 'unset'
      }
    },
    h: { base: `calc(100dvh - ${EDITOR_SPACING_MOBILE * 2}px)`, md: `calc(100dvh - ${EDITOR_SPACING * 2}px)` },
    maxW: 'auto',
    padding: { base: `${EDITOR_SPACING_MOBILE}px`, md: `${EDITOR_SPACING}px`, lg: `${EDITOR_SPACING}px` }
  };
  const contentContainerStyles: FlexProps = {
    justifyContent: 'center',
    h: '100%',
    gap: { base: `${EDITOR_SPACING_MOBILE}px`, md: `${EDITOR_SPACING}px` }
  };
  const panelContainerStyles: FlexProps = {
    marginLeft: 0,
    borderRadius: `${EDITOR_BORDER_RADIUS}px`
  };
  const containerStyles: FlexProps = {
    w: '100%',
    flexDir: 'column',
    maxW: { base: '100%', lg: 'calc(100% - 320px)' }
  };

  return {
    parentContainerStyles,
    contentContainerStyles,
    panelContainerStyles,
    containerStyles
  };
};

export const CONTENT_OUTLINE_BUTTON_STYLES: ButtonType = {
  borderColor: 'bg-color',
  borderRadius: 'full',
  borderWidth: 2,
  bottom: { base: 9, lg: 12 },
  boxShadow: '0px 4px 6px -1px #0000001A',
  fontSize: 'md',
  left: { base: 6, md: 'auto' },
  right: { base: 'auto', md: '7.5rem' },
  px: 3,
  py: 5,
  position: 'absolute',
  size: 'md'
};

export const CONTENT_OUTLINE_BUTTON_STYLES_XS: FlexProps = {
  borderRadius: 'full',
  position: 'absolute',
  bottom: '44px',
  right: '128px',
  border: 'white solid 3px',
  boxShadow: '3px 0 3px rgba(0, 0, 0, 0.05), -1px 0 1px rgba(0, 0, 0, 0.05)',
  boxSize: '52px'
};
