import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Flex } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import { useBreakpointValue } from '@linc-inc/yw-ui/hooks/common';
import { Button, Icon, Text } from '@linc-inc/yw-ui/ui';
import { safeString } from '@linc-inc/yw-ui/utils/return-save-data';
import { useSendInviteMutation } from '@reducers/organization';
import {
  organizationUserApi,
  OrganizationUserRoles,
  OrganizationUserStatusEnum,
  OrganizationUserType,
  useUpdateOrganizationUserMutation
} from '@reducers/organization-user';
import { useOrgUserRoleOptions } from '@hooks/select-options';
import { selectUser } from '@hooks/selectors';
import { useSuccessToast } from '@hooks/toasts';
import { useSubmitMutation } from '@hooks/use-submit-mutation';
import { Select } from '@common/select';
import { OrgUserCell } from '@components/organizations';
import { BackSquareIcon, EyeIcon, TrashOutlineIcon } from '@icons';
import { truncateLabel } from '@utils/truncate-label';
import { toast } from '@helpers/toast';
import { ORGANIZATIONS_PATH, USER_DETAILS_PATH } from '@routes/paths';
import DeleteOrgUserButton from '@containers/organizations/delete-org-user-button';
import { GLEEFUL_GREEN } from '@constants';
import { ALERT_STATUS } from '@typings';

export const DELETE_ORG_USER_BTN_ID = 'delete-org-user-btn-id';
export const FIRST_ORG_USER_BTN_ID = 'first-org-user-btn-id';
export const MAX_LENGTH_ORGANIZATION_NAME = 50;

type ButtonOptionsType = {
  leftIcon: any;
  label: string;
  href?: string;
  color?: string;
  colorSchema?: string;
  onClick?: () => void;
};

type UseGetPropertiesByStatusType = {
  statusColor: string;
  statusLabel: string;
  firstButton?: ButtonOptionsType;
  secondButton?: ButtonOptionsType;
  isRevoked: boolean;
};

const UseGetPropertiesByStatus = (
  status: OrganizationUserStatusEnum,
  email = '',
  orgId = '',
  userId = ''
): UseGetPropertiesByStatusType => {
  const { t } = useTranslation('organizations');
  const [mutation] = useSendInviteMutation();
  const dispatch = useDispatch();
  const sendInvites = useSubmitMutation({ mutation });
  const successCallback = () => {
    dispatch(organizationUserApi.util.invalidateTags(['org-users']));
    toast({
      status: ALERT_STATUS.SUCCESS,
      description: t<string>('invitationSuccess')
    });
  };

  const handleOnclickReInvite = (email: string) => {
    sendInvites({ payload: { emails: [email], role: 'user', orgId }, successCallback });
  };

  switch (status) {
    case OrganizationUserStatusEnum.PENDING:
      return {
        statusColor: 'orange500-orange400',
        statusLabel: 'invitationPending',
        firstButton: {
          onClick: () => handleOnclickReInvite(email),
          leftIcon: BackSquareIcon,
          label: t('reSendInvite')
        },
        secondButton: {
          color: 'text-medium-emphasis',
          colorSchema: undefined,
          leftIcon: TrashOutlineIcon,
          label: t('revoke')
        },
        isRevoked: true
      };
    case OrganizationUserStatusEnum.EXPIRED:
      return {
        statusColor: 'feedback-inspiring-red',
        statusLabel: 'invitationExpired',
        firstButton: {
          onClick: () => handleOnclickReInvite(email),
          leftIcon: BackSquareIcon,
          label: t('reSendInvite')
        },
        secondButton: {
          color: 'text-medium-emphasis',
          colorSchema: undefined,
          leftIcon: TrashOutlineIcon,
          label: t('revoke')
        },
        isRevoked: true
      };
    default:
      return {
        statusColor: GLEEFUL_GREEN[500],
        statusLabel: 'status.joined',
        firstButton: {
          href: `/${ORGANIZATIONS_PATH}/${orgId}/${USER_DETAILS_PATH}/${userId}`,
          leftIcon: EyeIcon,
          label: t('viewUser')
        },
        secondButton: {
          color: 'crimsonRed.600',
          colorSchema: undefined,
          leftIcon: TrashOutlineIcon,
          label: t('common:buttons.delete')
        },
        isRevoked: false
      };
  }
};

const ActionButtonByStatus = ({
  status,
  orgId,
  email,
  userId,
  rowInfo
}: {
  status: OrganizationUserStatusEnum;
  orgId: string;
  email: string | undefined;
  userId: string | undefined;
  rowInfo: OrganizationUserType;
}) => {
  const buttonActionProperty = UseGetPropertiesByStatus(status, email, orgId, userId);
  const actionsFirstButton = buttonActionProperty?.firstButton;
  const actionsSecondButton = buttonActionProperty?.secondButton;

  return (
    <>
      <Button
        asChild
        data-testid={`${FIRST_ORG_USER_BTN_ID}-${orgId}-${userId ?? email}`}
        px={4}
        variant="ghost"
        onClick={actionsFirstButton?.onClick}
      >
        <Link to={actionsFirstButton?.href ?? ''}>
          <Icon as={actionsFirstButton?.leftIcon} boxSize={4} />
          {actionsFirstButton?.label}
        </Link>
      </Button>
      <DeleteOrgUserButton {...rowInfo} isAdminsList={false} isRevoked={buttonActionProperty?.isRevoked}>
        <Button
          _hover={{ textDecoration: 'underline', color: actionsSecondButton?.color }}
          color={actionsSecondButton?.color}
          data-testid={`${DELETE_ORG_USER_BTN_ID}-${orgId}-${userId ?? email}`}
          px={0}
          variant="ghost"
        >
          <TrashOutlineIcon boxSize="1rem" />
          {actionsSecondButton?.label}
        </Button>
      </DeleteOrgUserButton>
    </>
  );
};

export const useOrgUsersColumns = () => {
  const navigate = useNavigate();
  const { id } = selectUser();
  const roleOptions = useOrgUserRoleOptions();
  const { t } = useTranslation('organizations');
  const columnHelper = createColumnHelper<OrganizationUserType>();
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const [mutation] = useUpdateOrganizationUserMutation();
  const updateOrgUserRole = useSubmitMutation({ mutation });
  const successCallback = useSuccessToast('organizationUsersServiceUpdateSuccess');

  return useMemo(() => {
    if (isMobile)
      return [
        columnHelper.accessor('user', {
          cell: info => {
            const { organization: org, email } = info.row.original;
            const userEmail = safeString(email);
            const user = info.getValue();
            const columnProps = { email: userEmail, ...user };

            return (
              <OrgUserCell
                isMobile
                {...columnProps}
                navigateToUser={() => navigate(`/${ORGANIZATIONS_PATH}/${org.id}/${USER_DETAILS_PATH}/${user?.id}`)}
              />
            );
          },
          header: () => null
        })
      ];

    return [
      columnHelper.accessor('user', {
        cell: info => {
          const { email } = info.row.original;
          const userEmail = safeString(email);
          const user = info.getValue();
          const columnProps = { email: userEmail, ...user };

          return <OrgUserCell {...columnProps} />;
        },
        header: t<string>('fields.name', { ns: 'common' })
      }),
      columnHelper.accessor('role', {
        cell: info => {
          const { organization: org, user, status } = info.row.original;
          const roleOption = roleOptions.find(option => option.value === info.getValue());
          const isDisabled = status !== OrganizationUserStatusEnum.ACTIVE || user?.id === id || !user;

          return (
            <Select
              defaultValue={roleOption}
              isDisabled={isDisabled}
              isSearchable={false}
              options={roleOptions}
              onChange={(e: any) => {
                if (user) {
                  updateOrgUserRole({
                    payload: { orgId: org.id, userId: user.id, role: e.value as OrganizationUserRoles },
                    successCallback
                  });
                }
              }}
            />
          );
        },
        header: t<string>('generic.role', { ns: 'common' })
      }),
      columnHelper.accessor('organization', {
        cell: info => truncateLabel(info.getValue().name, MAX_LENGTH_ORGANIZATION_NAME),
        header: t<string>('organizations')
      }),
      columnHelper.accessor('status', {
        cell: info => {
          const { status } = info.row.original;

          return (
            <Text color={UseGetPropertiesByStatus(status).statusColor}>
              {t<string>(UseGetPropertiesByStatus(status).statusLabel)}
            </Text>
          );
        },
        header: t<string>('status.invitationStatus')
      }),
      columnHelper.accessor('id', {
        id: 'actions',
        cell: info => {
          const { organization: org, user, status, email } = info.row.original;
          const userEmail = user?.email ?? email;

          return (
            <Flex gap={6} justifyContent="flex-end">
              <ActionButtonByStatus
                email={userEmail}
                orgId={org.id}
                rowInfo={info.row.original}
                status={status}
                userId={user?.id}
              />
            </Flex>
          );
        },
        header: () => null
      })
    ];
  }, [columnHelper, id, isMobile, navigate, roleOptions, successCallback, t, updateOrgUserRole]);
};
