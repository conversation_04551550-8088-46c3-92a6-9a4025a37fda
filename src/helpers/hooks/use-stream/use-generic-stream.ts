import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { useIsOffline } from '@hooks/network';
import { useSubmitMutation } from '@hooks/use-submit-mutation';
import { UseSubmitMutationType } from '@hooks/use-submit-mutation/types';
import { StreamStatusEnum } from '@typings';
import { onMessage, processDeltas } from './on-message';

import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';

type UseGenericStream<RequestType, ResponseType, TransformedResponse> = {
  statusToggle: ActionCreatorWithPayload<StreamStatusEnum, string>;
} & UseSubmitMutationType<RequestType, ResponseType, TransformedResponse>;

const DELAY_FOR_DELTAS = 2;
const COMPLETED_DELAY = 500;
const MANUAL_STOP = 'manual_stop';

export const useGenericStream = <RequestType, ResponseType, TransformedResponse>({
  statusToggle,
  mutation: streamTriggerMutation
}: UseGenericStream<RequestType, ResponseType, TransformedResponse>) => {
  const dispatch = useDispatch();
  const { isOffline } = useIsOffline();
  const [deltas, setDeltas] = useState<EventSourceMessage[]>([]);
  const triggerConnection = useSubmitMutation({ mutation: streamTriggerMutation });

  const unset = useCallback(() => dispatch(statusToggle(StreamStatusEnum.UNSET)), [dispatch, statusToggle]);
  const setError = useCallback(() => dispatch(statusToggle(StreamStatusEnum.ERROR)), [dispatch, statusToggle]);
  const setStopped = useCallback(() => dispatch(statusToggle(StreamStatusEnum.STOPPED)), [dispatch, statusToggle]);
  const setLoading = useCallback(() => dispatch(statusToggle(StreamStatusEnum.LOADING)), [dispatch, statusToggle]);
  const setCompleted = useCallback(() => dispatch(statusToggle(StreamStatusEnum.COMPLETED)), [dispatch, statusToggle]);
  const setTriggerError = useCallback(
    () => dispatch(statusToggle(StreamStatusEnum.TRIGGER_ERROR)),
    [dispatch, statusToggle]
  );

  const resetStream = useCallback(() => {
    unset();
    setDeltas([]);
  }, [unset]);

  useEffect(() => {
    if (isOffline) setError();
  }, [isOffline, setError]);

  const openConnection = useCallback(
    async (path: string, isReconnect?: boolean) => {
      setLoading();

      let seconds = 0;
      let isError = false;
      let isManuallyStopped = false;
      const ctrl = new AbortController();

      /**
       * Closes the stream if there are no deltas for a certain period of time
       * Required for reconnection logic
       */
      /* istanbul ignore next */
      const interval = setInterval(() => {
        seconds += 1;
        if (seconds > DELAY_FOR_DELTAS || !navigator.onLine) {
          ctrl.abort('stream is closed');
          clearInterval(interval);
          setCompleted();
        }
      }, 1000);

      /* istanbul ignore next */
      await fetchEventSource(path, {
        method: 'GET',
        openWhenHidden: true,
        credentials: 'include',

        onclose: () => {
          clearInterval(interval);
          if (isError) return setError();
          if (isManuallyStopped) return setStopped();
          setTimeout(() => setCompleted(), COMPLETED_DELAY);
        },

        onmessage: event => {
          if (event.event === 'error') {
            isError = true;
            ctrl.abort('stream is closed');
          }
          if (event.id !== '') clearInterval(interval);
          if (event.data === MANUAL_STOP) isManuallyStopped = true;
          if (!isReconnect && event.data !== MANUAL_STOP) onMessage(event, setDeltas);
        },

        onerror: error => {
          setError();
          clearInterval(interval);
          throw error; // rethrow to stop the stream
        }
      });
    },
    [setCompleted, setError, setLoading, setStopped]
  );

  /**
   * Triggers mutation and starts stream connection
   */
  const onSubmit = useCallback(
    ({
      connectionPath,
      ...params
    }: Omit<Parameters<typeof triggerConnection>[0], 'successCallback' | 'errorCallback'> & {
      connectionPath: string;
    }) => {
      setDeltas([]);

      return triggerConnection({
        successCallback: () => {
          openConnection(connectionPath);
        },
        errorCallback: setTriggerError,
        ...params
      });
    },
    [openConnection, setTriggerError, triggerConnection]
  );

  /**
   * Used to reconnect the stream if lost
   * Deltas won't be stored due to missing information during the disconnection
   */
  const reconnect = useCallback(
    async (path: string, callback: VoidFunction) => {
      resetStream();
      await openConnection(path, true)
        .catch(error => error)
        .finally(() => callback());
    },
    [openConnection, resetStream]
  );

  const stream = useMemo(() => processDeltas(deltas), [deltas]);

  return { stream, onSubmit, resetStream, reconnect, openConnection };
};
