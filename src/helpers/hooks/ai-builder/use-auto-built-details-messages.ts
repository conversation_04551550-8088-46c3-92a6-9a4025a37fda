import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { camelCase } from 'lodash';
import { useGenerateToolDetailsMutation, useGenerateToolGoalMutation } from '@reducers/tools';
import { useSubmitMutation } from '@hooks/use-submit-mutation';
import { buildStepEnum, ConversationMessageType } from '@components/chat';
import { ChatPromptFormType } from '@components/chat-prompt/types';
import { useAutoBuildContext } from './use-auto-built-details-context';

const handleLastMessage = (prev: ConversationMessageType[], message: string): ConversationMessageType[] => {
  const lastIdx = prev.length - 1;
  const messages = prev.map(msg => ({ ...msg }));
  messages[Number(lastIdx)].isLoading = false;
  messages[Number(lastIdx)].message = message;

  return messages;
};

const addResponseMessage = (
  prev: ConversationMessageType[],
  prompt: string,
  isDescriptionRequest: boolean
): ConversationMessageType[] => {
  const messages = prev.map(msg => ({ ...msg }));
  messages.push({
    id: `user-${isDescriptionRequest ? 'description' : 'name'}-input`,
    message: prompt,
    isUserMessage: true
  });
  messages.push({
    id: `ai-${isDescriptionRequest ? 'confirmation' : 'description'}-request`,
    message: '',
    isLoading: true,
    showActions: false
  });

  return messages;
};

export const useAutoBuiltDetailsMessages = () => {
  const { t } = useTranslation('ai-builder');
  const bottomEl = useRef<HTMLDivElement | null>(null);
  const { toolId, kind, isToolAvailable } = useAutoBuildContext();
  const [messages, setMessages] = useState<ConversationMessageType[]>([]);
  const [currentBuildStep, setCurrentBuildStep] = useState<buildStepEnum>(buildStepEnum.goal);
  const [nameMutation, { data: nameResponse }] = useGenerateToolGoalMutation();
  const [descriptionMutation, { data: detailsResponse }] = useGenerateToolDetailsMutation();
  const requestName = useSubmitMutation({ mutation: nameMutation });
  const requestDescription = useSubmitMutation({ mutation: descriptionMutation });

  const updateLastAiMessage = useCallback((message: string) => {
    setMessages(prev => handleLastMessage(prev, message));
    //bottomEl?.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const generateGoal = useCallback(
    (request: string) =>
      requestName({
        payload: { request, toolId },
        successCallback: name => {
          setCurrentBuildStep(buildStepEnum.details);
          updateLastAiMessage(t<string>('details.generateDetailsMessage', { name }));
        }
      }),
    [requestName, t, toolId, updateLastAiMessage]
  );

  const generateDetails = useCallback(
    (request: string) =>
      requestDescription({
        payload: { request, toolId },

        successCallback: () => {
          setCurrentBuildStep(buildStepEnum.audiences);
        }
      }),
    [requestDescription, toolId]
  );

  const handleEmbeddingsMessage = useCallback(() => {
    setCurrentBuildStep(buildStepEnum.embeddings);
  }, []);

  const onSubmit = useCallback(
    ({ prompt }: ChatPromptFormType, resetForm: VoidFunction) => {
      setMessages(prev => addResponseMessage(prev, prompt, Boolean(nameResponse)));
      resetForm();

      if (nameResponse) return generateDetails(prompt);

      return generateGoal(prompt);
    },
    [generateDetails, generateGoal, nameResponse]
  );

  useEffect(() => {
    if (isToolAvailable && messages.length === 0) {
      const initialMessages: ConversationMessageType[] = [
        {
          id: 'ai-welcome-request',
          showActions: false,
          message: t<string>('details.welcomeMessage')
        },
        {
          id: 'ai-name-request',
          showActions: false,
          message: t<string>('details.generateGoalMessage', { kind: t(`details.kind.${camelCase(kind)}`) })
        }
      ];
      setMessages(initialMessages);
    }
  }, [isToolAvailable, kind, messages.length, t]);

  return useMemo(
    () => ({
      bottomEl,
      onSubmit,
      messages,
      detailsResponse,
      currentBuildStep,
      handleEmbeddingsMessage,
      setMessages
    }),
    [bottomEl, onSubmit, messages, detailsResponse, currentBuildStep, handleEmbeddingsMessage]
  );
};
