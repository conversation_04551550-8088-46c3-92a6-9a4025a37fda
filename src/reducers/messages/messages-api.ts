import { createApi } from '@reduxjs/toolkit/query/react';
import { handleQueryParams } from '@utils/handle-query-params';
import { REVERSE_INFINITE_SCROLL_CONFIG } from '@utils/infinite-scroll-config';
import { AXIOS_METHODS, axiosBaseQuery } from '@helpers/axios';
import { translationKeyFromError } from '@helpers/error-handler';
import { BASE_URL, CHAT_MESSAGES_URL } from '@constants';
import { QueryArgsType } from '@typings';
import {
  CreateMessageRequestType,
  CreateMessageResponseType,
  PaginatedConversationType,
  PaginatedMessagesType
} from './types';

export const messagesApi = createApi({
  reducerPath: 'messagesApi',
  baseQuery: axiosBaseQuery<unknown>({ baseUrl: BASE_URL }),
  tagTypes: ['Messages'],
  endpoints: build => ({
    getMessages: build.query<PaginatedConversationType, QueryArgsType & { chatId: string }>({
      query: ({ chatId, ...queryParams }) => ({
        url: `${CHAT_MESSAGES_URL.replace('id', chatId)}${handleQueryParams(queryParams)}`,
        method: AXIOS_METHODS.GET
      }),
      transformResponse: (response: PaginatedMessagesType): PaginatedConversationType => {
        const data = response.data
          .map(({ id, content, createdBy }) => ({
            id,
            message: content,
            isUserMessage: createdBy === 'user'
          }))
          .reverse();

        return { data, meta: response.meta };
      },
      //...REVERSE_INFINITE_SCROLL_CONFIG,
      providesTags: ['Messages'],
      transformErrorResponse: translationKeyFromError
    }),
    createMessage: build.mutation<CreateMessageResponseType, CreateMessageRequestType>({
      query: ({ chatId, content }) => ({
        url: CHAT_MESSAGES_URL.replace('id', chatId),
        method: AXIOS_METHODS.POST,
        data: { content }
      }),
      invalidatesTags: ['Messages'],
      transformErrorResponse: translationKeyFromError
    })
  })
});

export const { useGetMessagesQuery, useCreateMessageMutation } = messagesApi;
