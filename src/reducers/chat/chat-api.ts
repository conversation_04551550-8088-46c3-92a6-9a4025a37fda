import { createApi } from '@reduxjs/toolkit/query/react';
import { AXIOS_METHODS, axiosBaseQuery } from '@helpers/axios';
import { translationKeyFromError } from '@helpers/error-handler';
import { BASE_URL, CHAT_URL, DEFAULT_PAGINATION_COLUMN, DEFAULT_PAGINATION_LIMIT } from '@constants';
import { GenericResponseIdType } from '@typings';
import { ChatList, ChatType } from './types';

export const chatApi = createApi({
  reducerPath: 'chatApi',
  baseQuery: axiosBaseQuery<unknown>({ baseUrl: BASE_URL }),
  tagTypes: ['Chat'],
  endpoints: build => ({
    fetchChats: build.query<ChatList, { page: number; limit?: number }>({
      query: ({ page, limit = DEFAULT_PAGINATION_LIMIT }) => ({
        url: `${CHAT_URL}?page=${page}&limit=${limit}&sortBy=${DEFAULT_PAGINATION_COLUMN}`,
        method: AXIOS_METHODS.GET
      }),
      providesTags: ['Chat'],
      // ...INFINITE_SCROLL_CONFIG,
      transformErrorResponse: translationKeyFromError
    }),
    getChat: build.query<ChatType, string>({
      query: id => ({
        url: `${CHAT_URL}/${id}`,
        method: AXIOS_METHODS.GET
      }),
      providesTags: ['Chat'],
      transformErrorResponse: translationKeyFromError
    }),
    createChat: build.mutation<GenericResponseIdType, void>({
      query: () => ({
        url: CHAT_URL,
        method: AXIOS_METHODS.POST
      }),
      invalidatesTags: ['Chat'],
      transformErrorResponse: translationKeyFromError
    }),
    deleteChat: build.mutation<string, string>({
      query: chatId => ({
        url: `${CHAT_URL}/${chatId}`,
        method: AXIOS_METHODS.DELETE
      }),
      transformResponse: (response: GenericResponseIdType) => response.translationKey,
      transformErrorResponse: translationKeyFromError
    })
  })
});

export const {
  useGetChatQuery,
  useFetchChatsQuery,
  useCreateChatMutation,
  useDeleteChatMutation,
  reducerPath: chatApiReducerPath,
  reducer: chatApiReducer,
  middleware: chatApiMiddleware
} = chatApi;
