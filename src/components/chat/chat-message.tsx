import { memo, PropsWithChildren, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { ImageAvatar } from '@linc-inc/yw-ui/components/avatars';
import { Box, Button, HStack, Separator, VStack } from '@linc-inc/yw-ui/ui';
import { useCopyToClipboard } from '@hooks/use-copy-to-clipboard';
import { CopyIcon } from '@icons';
import { MessageBody } from './message-body';
import { ChatMessageType } from './types';

export const COPY_BUTTON_ID = 'copy-button-id';

const ChatMessageAvatar = ({
  isUserMessage,
  imageSource,
  botAvatar
}: {
  imageSource: string;
  isUserMessage: boolean;
  botAvatar: ReactNode;
}) => {
  if (isUserMessage) return <ImageAvatar imageSource={imageSource} size={{ base: 'xs', sm: 'sm' }} />;

  return botAvatar;
};

const ChatMessage = memo((rest: PropsWithChildren<ChatMessageType>) => {
  const { showActions = true, isLoading = false, isUserMessage = false, imageSource = '', message, botAvatar } = rest;
  const { t } = useTranslation();
  const copyToClipboard = useCopyToClipboard(message);
  const displayActions = showActions && !isUserMessage && !isLoading;

  console.log('i was rerender:', message);

  return (
    <HStack
      aria-data={isUserMessage}
      bgColor={isUserMessage ? 'background' : 'white500-gray800'}
      flexDir={isUserMessage ? 'row-reverse' : 'row'}
      pl={{ base: 0, sm: 2 }}
      pr={{ base: 1, sm: 4 }}
      py={6}
      w="100%"
    >
      <Box alignSelf="flex-start">
        <ChatMessageAvatar botAvatar={botAvatar} imageSource={imageSource} isUserMessage={isUserMessage} />
      </Box>

      <VStack alignItems="stretch" flex="1 1 auto" pt={2}>
        <MessageBody {...rest} />

        <Separator orientation="horizontal" visibility={displayActions ? 'visible' : 'hidden'} w="100%" />
        <Button
          alignSelf="flex-start"
          color="text-medium-emphasis"
          data-testid={COPY_BUTTON_ID}
          fontWeight="normal"
          h="auto"
          p={0}
          size="xs"
          variant="plain"
          visibility={displayActions ? 'visible' : 'hidden'}
          onClick={copyToClipboard}
        >
          <CopyIcon size="sm" />
          {t('buttons.copy')}
        </Button>
      </VStack>
    </HStack>
  );
});

export default ChatMessage;
