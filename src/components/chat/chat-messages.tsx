import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Button, Flex } from '@linc-inc/yw-ui/ui';
import { ChatStatusType, setChatStreamStatus, useGetChatQuery } from '@reducers/chat';
import { messagesApi, useGetMessagesQuery } from '@reducers/messages';
import { setFocusPrompt } from '@reducers/utils';
import { useIsOffline } from '@hooks/network';
import { useChatStreamStatus } from '@hooks/selectors';
import { useErrorToast } from '@hooks/toasts';
import { useGenericStream } from '@hooks/use-stream/use-generic-stream';
import { LOADING_DOT_TAG_TO_REPLACE, LoadingSpinner } from '@common/loaders';
import { ChatConversation, ChatMessagesProps, HandleMessagesType } from '@components/chat';
import { ChatPromptForm } from '@components/chat-prompt/chat-prompt-form';
import { ChatPromptFormType } from '@components/chat-prompt/types';
import InfiniteScrollWrapper from '@components/infinite-scroll';
import { StopStream } from '@containers/global';
import { BASE_URL } from '@constants';

export const CHAT_PROMPT_FORM = 'expandible-textarea-id';
export const CHAT_RETRY_BUTTON = 'chat-retry-button';

const SCROLL_CONVERSATION_DELAY = 800;
const SORT_PARAMS = ['createdAt:DESC'];
const CHAT_CONVERSATION_SCROLL_ID = 'chat-conversation-scroll-id';

const useIsReadonlyChat = (currentChatId: string) => {
  const dispatch = useDispatch();
  const { isChatStreamCompleted } = useChatStreamStatus();
  const maxTokensToast = useErrorToast('maxTokensError', { content: 'chat' });
  const { currentData } = useGetChatQuery(currentChatId, { skip: !currentChatId });
  const isReadOnly = currentData?.status === ChatStatusType.READ_ONLY;

  // useEffect(() => {
  //   if (isChatStreamCompleted) dispatch(chatApi.util.invalidateTags(['Chat']));
  // }, [dispatch, isChatStreamCompleted]);

  useEffect(() => {
    if (currentChatId && isReadOnly) maxTokensToast();
  }, [currentChatId, isReadOnly, maxTokensToast]);

  return Boolean(currentChatId) && isReadOnly;
};

const useHandleMessages = ({ stream, lastPrompt, page, currentChatId }: HandleMessagesType) => {
  const { isChatStreamError, isChatStreamLoading, isChatStreamCompleted } = useChatStreamStatus();
  const { currentData, isLoading, refetch } = useGetMessagesQuery(
    { chatId: currentChatId, sortParams: SORT_PARAMS, page },
    { skip: !currentChatId }
  );

  useEffect(() => {
    if (isChatStreamCompleted) {
      refetch();
    }
  }, [isChatStreamCompleted, refetch]);

  const messages = [...(currentData?.data ?? [])];
  const hasMore = (currentData?.meta.totalPages ?? page) > page;

  if (isChatStreamError) messages.push({ id: 'error-stream', isError: true });

  if (isChatStreamLoading || (isLoading && stream)) {
    // if (messages.length === 0) messages.push({ id: 'initial-message', message: lastPrompt, isUserMessage: true });
    messages.push({
      id: 'current-stream',
      isLoading: isChatStreamLoading,
      message: `${stream} ${isChatStreamLoading ? LOADING_DOT_TAG_TO_REPLACE : ''}`
    });
  }

  return { messages, hasMore, isMessagesLoading: isLoading, refetchMessages: refetch };
};

export const ChatMessages = ({
  emptyState,
  botAvatar,
  createMessageMutation,
  createChatMutation,
  streamUrl,
  currentChatId,
  onCreateChat
}: ChatMessagesProps) => {
  const [page, setPage] = useState(1);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { isOffline } = useIsOffline();
  const isReadOnlyChat = useIsReadonlyChat(currentChatId);
  const [lastPrompt, setLastPrompt] = useState('');
  const [isRetrying, setIsRetrying] = useState(false);
  const { isChatStreamError, isChatStreamLoading, isChatStreamCompleted, isChatStreamTriggeredError } =
    useChatStreamStatus();
  const [createMessage, isSuccess] = createMessageMutation;
  const { currentData, isFetching, refetch } = useGetMessagesQuery(
    { chatId: currentChatId, sortParams: SORT_PARAMS, page },
    { skip: !currentChatId }
  );

  const { stream, onSubmit, resetStream, reconnect, openConnection } = useGenericStream({
    statusToggle: setChatStreamStatus,
    mutation: createMessage
  });

  const createdRef = useRef(false);
  const triggeredRef = useRef(false);

  const handleCreate = async ({ chatId, content, resetForm }) => {
    resetStream();
    await createMessage({ chatId, content }).unwrap();
    createdRef.current = true;
    triggeredRef.current = false;
    refetch(); // force update if needed immediately
    resetForm();
  };

  useEffect(() => {
    if (createdRef.current && isSuccess && !isFetching && !triggeredRef.current) {
      triggeredRef.current = true;
      createdRef.current = false;

      const connectionPath = `${BASE_URL}${streamUrl.replace('id', currentChatId)}`;
      // ✅ Trigger your event here
      openConnection(connectionPath);
    }
  }, [currentData, isSuccess, isFetching, openConnection]);

  const { messages, hasMore, isMessagesLoading, refetchMessages } = useHandleMessages({
    stream,
    lastPrompt,
    page,
    currentChatId
  });
  /*
    useEffect(() => {
      if (isSuccess) {
        refetch();
      }
    }, [isSuccess, refetch]); */

  const onSubmitMessage = useCallback(
    async (values: ChatPromptFormType, resetForm: VoidFunction) => {
      if (isChatStreamLoading) return;

      const content = values.prompt.replaceAll('\n', '\n\n');
      setPage(1);
      setLastPrompt(content);

      let chatId = currentChatId;
      if (!chatId) {
        const response = await createChatMutation();
        chatId = response.id;
        onCreateChat(chatId);
      }

      // onSubmit({ connectionPath, payload: { chatId, content }, finallyCallback: () => resetForm() });

      handleCreate({ chatId, content, resetForm });
    },
    [createChatMutation, currentChatId, isChatStreamLoading, onSubmit, setPage, streamUrl, onCreateChat]
  );

  const retryOnError = useCallback(() => {
    const connectionPath = `${BASE_URL}${streamUrl}`;

    // Handles create message error
    if (isChatStreamTriggeredError && lastPrompt)
      return onSubmit({
        connectionPath,
        payload: { chatId: currentChatId, content: lastPrompt },
        finallyCallback: () => setLastPrompt('')
      });

    // Handles lost stream connection
    setIsRetrying(true);
    reconnect(connectionPath, () => {
      setPage(1);
      refetchMessages()
        .unwrap()
        .catch(err => err)
        .finally(() => setIsRetrying(false));
    });
  }, [currentChatId, isChatStreamTriggeredError, lastPrompt, streamUrl, onSubmit, reconnect, refetchMessages, setPage]);

  // Cleans state for new chats
  useEffect(() => {
    if (!currentChatId) {
      setPage(1);
      resetStream();
      dispatch(messagesApi.util.resetApiState());
    }
  }, [currentChatId, dispatch, resetStream, setPage]);

  /**
   * Handles conversation scrolling
   * Necessary delay to avoid issues with the scrolling of the conversation
   */
  useEffect(() => {
    if (isChatStreamLoading) dispatch(setFocusPrompt(false));
    // let timeout: NodeJS.Timeout;
    /* istanbul ignore if */
    if (isChatStreamCompleted) {
      // timeout = setTimeout(() => {
      dispatch(setFocusPrompt(true));
      // clearTimeout(timeout);
      // }, SCROLL_CONVERSATION_DELAY);
    }
  }, [dispatch, isChatStreamCompleted, isChatStreamLoading]);

  // Resets stream when a new thread is selected
  useEffect(() => {
    if (currentChatId) {
      setPage(1);
      resetStream();
      refetchMessages();
    }
  }, [currentChatId, refetchMessages, resetStream, setPage]);

  if (isMessagesLoading) return <LoadingSpinner />;

  return (
    <Flex
      flexDir="column"
      h="100%"
      justifyContent="space-between"
      maxH={{ base: 'calc(100dvh - 7.56rem)', md: 'calc(100dvh - 13.75rem)' }}
    >
      <Flex flexDir="column" h="100%" overflow="auto" position="relative" w="full">
        <InfiniteScrollWrapper
          inverse
          dataLength={messages.length}
          flexProps={{ width: '100%', flexDirection: 'column-reverse' }}
          forceRender={isChatStreamCompleted}
          hasMore={hasMore}
          id={CHAT_CONVERSATION_SCROLL_ID}
          key={currentChatId}
          loader={<LoadingSpinner />}
          next={/* istanbul ignore next */ () => setPage(prev => prev + 1)}
          style={{ display: 'flex', flexDirection: 'column-reverse' }}
        >
          <ChatConversation botAvatar={botAvatar} emptyState={emptyState} messages={messages} />
        </InfiniteScrollWrapper>
        {(isChatStreamLoading || true) && <StopStream id={currentChatId} source="chat" />}
      </Flex>
      {isChatStreamError && (
        <Flex justifyContent="center" my={4} w="100%">
          <Button data-testid={CHAT_RETRY_BUTTON} disabled={isOffline} loading={isRetrying} onClick={retryOnError}>
            {t('buttons.tryAgain')}
          </Button>
        </Flex>
      )}
      <ChatPromptForm
        data-testid={CHAT_PROMPT_FORM}
        isSubmitBlocked={isReadOnlyChat || isRetrying}
        placeholder={t<string>('chat:chatPrompt.askMeAnything')}
        onSubmit={onSubmitMessage}
      />
    </Flex>
  );
};
