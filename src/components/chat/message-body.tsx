import { PropsWithChildren } from 'react';
import { Box } from '@chakra-ui/react';
import Markdown from '@components/markdown';
import { ChatMessageType } from './types';

export const MESSAGE_BODY_ID = 'message-body-id';

export const MessageBody = ({ isUserMessage = false, message, children }: PropsWithChildren<ChatMessageType>) => {
  return (
    <Box
      className="section-to-print"
      color="text-high-emphasis"
      css={{
        table: {
          width: '100%',
          display: 'block',
          overflowX: 'scroll'
        },
        'p:last-of-type': {
          marginBottom: '0 !important',
          marginRight: '0.75rem',
          textAlign: isUserMessage ? 'right' : 'left'
        }
      }}
      data-testid={MESSAGE_BODY_ID}
      fontSize={{ base: '0.875rem', sm: '1rem' }}
      id={MESSAGE_BODY_ID}
      ml={isUserMessage ? 2 : 0}
      width="100%"
    >
      <Markdown strongBreak={false} value={message} />
      {children}
    </Box>
  );
};
