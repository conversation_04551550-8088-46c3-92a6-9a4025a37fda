import { memo, PropsWithChildren } from 'react';
import { Flex } from '@linc-inc/yw-ui/ui';
import { StreamErrorAlert } from '@containers/global';
import ChatMessage from './chat-message';
import SkeletonConversation from './skeleton-conversation';
import { ConversationMessageType, ConversationType } from './types';

export const ERROR_MESSAGE_ID = 'error-message-id';

const ConversationMessage = memo(({ isError, message = '', ...rest }: PropsWithChildren<ConversationMessageType>) => {
  if (isError) return <StreamErrorAlert />;

  return <ChatMessage message={message} {...rest} />;
});

ConversationMessage.displayName = 'ConversationMessage';

export const BaseConversation = ({ isLoading, messages = [], emptyState, botAvatar }: ConversationType) => {
  if (isLoading) return <SkeletonConversation />;
  if (messages.length === 0) return emptyState;

  return (
    <Flex
      alignItems="flex-start"
      css={{
        '& > [aria-data="true"]:last-child': {
          marginBottom: '114px'
        }
      }}
      flexDir="column"
      h="100%"
      overflowY="auto"
      pt={4}
      // scrollBehavior="auto"
    >
      {messages.map((message, index) => {
        return (
          <ConversationMessage
            {...message}
            botAvatar={botAvatar}
            key={index === messages.length - 1 && !message.isUserMessage ? 'current-stream' : message.id}
          >
            {message.children}
          </ConversationMessage>
        );
      })}
    </Flex>
  );
};

export const ChatConversation = ({ isLoading, messages = [], emptyState, botAvatar }: ConversationType) => {
  if (isLoading) return <SkeletonConversation />;
  if (messages.length === 0) return emptyState;

  return (
    <Flex alignItems="flex-start" flexDir="column" h="100%" overflowY="auto" pt={4}>
      {messages.map((message, index) => (
        <ConversationMessage
          {...message}
          botAvatar={botAvatar}
          children={message.children}
          key={message.id}
        ></ConversationMessage>
      ))}
    </Flex>
  );
};
