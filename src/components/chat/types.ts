import { ReactNode } from 'react';
import { MutationTrigger } from '@reduxjs/toolkit/dist/query/react/buildHooks';
import { MutationDefinition } from '@reduxjs/toolkit/query';
import { CreateMessageRequestType, CreateMessageResponseType } from '@reducers/messages/types';
import { BaseQueryFunctionType } from '@helpers/axios';
import { GenericResponseIdType } from '@typings';

export type FlashingDotType = { delay?: number };

export type SkeletonMessageType = { isUserMessage?: boolean };

export type ChatMessageType = {
  showActions?: boolean;
  isUserMessage?: boolean;
  message: string;
  imageSource?: string;
  isLoading?: boolean;
  onStopGenerating?: VoidFunction;
  botAvatar: ReactNode;
};

export interface ConversationMessageType extends Omit<ChatMessageType, 'message'> {
  id: string;
  message?: string;
  isError?: boolean;
  isLoading?: boolean;
  isUserMessage?: boolean;
  children?: ReactNode;
}

export type ConversationType = {
  isLoading?: boolean;
  messages?: ConversationMessageType[];
  suggestions?: string[];
  children?: ReactNode;
  emptyState: ReactNode;
  botAvatar: ReactNode;
};

export type MobileIconsProps = {
  newChat: string;
  addChatCallback: () => void;
};

export type DesktopIconsProps = {
  newChat: string;
};

export type ThreadType = {
  title: string;
  selected?: boolean;
  id: string;
  selectThread: VoidFunction;
  deleteThread: VoidFunction;
};

export type NewChatButtonProps = {
  children: string;
  onClick: VoidFunction;
  disabled?: boolean;
};

export enum buildStepEnum {
  goal = 'GOAL',
  details = 'DETAILS',
  audiences = 'AUDIENCES',
  embeddings = 'EMBEDDINGS'
}

export type HandleMessagesType = {
  stream: string;
  lastPrompt: string;
  currentChatId: string;
};

export type CreateMessageMutationType = MutationTrigger<
  MutationDefinition<
    CreateMessageRequestType,
    BaseQueryFunctionType<unknown>,
    string,
    CreateMessageResponseType,
    string
  >
>;

export type ChatMessagesProps = {
  emptyState?: ReactNode;
  botAvatar?: ReactNode;
  createMessageMutation: any;
  createChatMutation: () => Promise<GenericResponseIdType>;
  streamUrl: string;
  currentChatId: string;
  onCreateChat: (chatId: string) => void;
};

export type EmptyStateProps = {
  options?: string[];
  icon?: ReactNode;
  title?: string;
  subtitle?: string;
};
