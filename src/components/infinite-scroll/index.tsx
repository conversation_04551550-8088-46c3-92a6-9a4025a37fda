import * as React from 'react';
import InfinityS<PERSON>roll, { Props } from 'react-infinite-scroll-component';
import { Flex, FlexProps } from '@chakra-ui/react';

export const YW_SCROLL_ID = 'yw-scroll-id';

export default function InfiniteScrollWrapper({
  style,
  id,
  forceRender,
  children,
  flexProps,
  ...props
}: React.JSX.IntrinsicAttributes &
  React.JSX.IntrinsicClassAttributes<InfinityScroll> &
  Readonly<Props> & { forceRender: boolean; id: string; flexProps?: FlexProps }) {
  const scrollableTarget = `${id}-${YW_SCROLL_ID}`;

  return (
    <Flex
      data-testid={scrollableTarget}
      flexDir="column"
      id={scrollableTarget}
      key={`${forceRender}`}
      overflowY="auto"
      {...flexProps}
    >
      <div
        {...props}
        // scrollableTarget={scrollableTarget}
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          ...style
        }}
      >
        {children}
      </div>
    </Flex>
  );
}
