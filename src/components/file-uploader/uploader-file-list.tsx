import { useCallback, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { FileTag, FileTagList } from '@linc-inc/yw-ui/components/uploader';
import { useBoolean } from '@linc-inc/yw-ui/hooks/common/use-boolean';
import { Button } from '@linc-inc/yw-ui/ui';
import { ACTION_BUTTON_TEST_ID, ConfirmationModal } from '@common/modals';
import { ModalCheckboxAction } from '@common/modals/modal-checkbox-action';
import { useFileUploaderContext } from './uploader-context';

const DEFAULT_DELETE_FROM_SYSTEM = false;

export const UploaderFileList = () => {
  const { t } = useTranslation('common');
  const [fileToDelete, setFileToDelete] = useState<{ id: string; fileName: string } | null>(null);
  const [deleteFromSystem, setDeleteFromSystem] = useState(DEFAULT_DELETE_FROM_SYSTEM);
  const { files, removeFile } = useFileUploaderContext();
  const [isOpen, { on: onOpen, off: onClose }] = useBoolean();

  const handleOnRemove = useCallback(
    (fileId: string, deleteFromSystem: boolean) => {
      removeFile?.(fileId, deleteFromSystem);
      setDeleteFromSystem(DEFAULT_DELETE_FROM_SYSTEM);
      onClose();
    },
    [removeFile, onClose]
  );

  if (!files || files.length === 0) {
    return null;
  }

  return (
    <>
      <FileTagList title={t<string>('uploader.uploadedFiles')}>
        {files.map(file => (
          <FileTag
            key={file.id}
            onClose={() => {
              setFileToDelete(file);
              onOpen();
            }}
          >
            {file.fileName}
          </FileTag>
        ))}
      </FileTagList>

      <ConfirmationModal
        actions={
          <ModalCheckboxAction
            callback={setDeleteFromSystem}
            checkboxText={t<string>('files:deleteFromSystem.check')}
            defaultValue={deleteFromSystem}
            id={fileToDelete?.id}
            warningText={t<string>('files:deleteFromSystem.alertMessage')}
          />
        }
        body={
          <Trans
            components={{ bold: <strong /> }}
            i18nKey="files:deleteFileModalBody"
            values={{ fileName: fileToDelete?.fileName }}
          />
        }
        header={t<string>('files:deleteFileModalHeader')}
        isOpen={isOpen}
        onClose={onClose}
      >
        <Button
          colorPalette="crimsonRed"
          data-testid={ACTION_BUTTON_TEST_ID}
          size={{ base: 'sm', md: 'md' }}
          type="button"
          onClick={() => fileToDelete?.id && handleOnRemove(fileToDelete.id, deleteFromSystem)}
        >
          {t('buttons.delete')}
        </Button>
      </ConfirmationModal>
    </>
  );
};
