import { Field, FieldProps } from 'formik';
import { TextAreaInput } from '@common/inputs';
import { isInvalidField } from '@utils/is-invalid-field';
import { DEFAULT_INPUT_SIZES } from '@constants';
import { TextAreaFieldType } from './types';

const TextAreaField = ({ fieldName, fieldProps = {}, isVisible, ...rest }: TextAreaFieldType) => {
  return (
    <Field name={fieldName} {...fieldProps}>
      {({ field, form }: FieldProps) => {
        const { isInvalid, error } = isInvalidField(fieldName, form);
        const { textAreaProps = {}, formControlProps = {}, ...inputRest } = rest;

        return (
          <TextAreaInput
            errorMessage={error}
            formControlProps={{ isInvalid, ...formControlProps }}
            isVisible={isVisible}
            textAreaProps={{ ...field, ...textAreaProps, size: DEFAULT_INPUT_SIZES }}
            {...inputRest}
          />
        );
      }}
    </Field>
  );
};

export default TextAreaField;
