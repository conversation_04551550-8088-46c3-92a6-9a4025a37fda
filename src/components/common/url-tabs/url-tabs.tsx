import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { useBreakpointValue } from '@linc-inc/yw-ui/hooks/common/use-breakpoint-value';
import { Box, Flex, MenuContent, MenuItem, MenuRoot, MenuTrigger, Tabs, TabsProps, Text } from '@linc-inc/yw-ui/ui';
import { chooseFlexible, safeString } from '@linc-inc/yw-ui/utils/return-save-data';
import { TabItem } from '@common/tabs';
import { ArrowDownIcon } from '@icons';
import { handleQueryParams } from '@utils/handle-query-params';
import { CONTENT_PATH, DOC_FILES_PATH } from '@routes/paths';
import { useAiCollectionContext } from '@containers/ai-collection/ai-collection-header/context';
import { ToolsTabsEnum } from '@containers/ai-collection/ai-collection-header/types';
import { TabsConfigType } from './types';

const IS_ORG_VIEW_PARAM = '&isOrgView=true';

export const COLLECTION_HEADER_MENU_FILTER = 'collection-header-menu-filter';
export const COLLECTION_HEADER_TABS_ID = 'collection-header-tabs-id';
export const MENU_CURRENT_VALUE_TEXT = 'menu-current-value-text';

export const UrlTabs = ({ tabsConfig }: { tabsConfig: TabsConfigType }) => {
  const { t } = useTranslation(['ai-collection']);
  const [urlParams] = useSearchParams();
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const isMobile = useBreakpointValue({ base: true, md: false });
  const { searchParam, tabSelected, setPage } = useAiCollectionContext();
  const isMyDrive = pathname === `/${CONTENT_PATH}` || pathname === `/${DOC_FILES_PATH}`;

  const cleanFilterParams = useCallback(() => {
    urlParams.set('contents.user.id', '');
    urlParams.set('createdBy.id', '');
    urlParams.set('visibility', '');
    urlParams.set('createdAt', '');
    urlParams.set('author', '');
    urlParams.set('isOrgView', '');
    urlParams.set('tabSelected', '');
  }, [urlParams]);

  const filterUrl = (
    filterParams: string[],
    defaultSort: string[],
    tabSelected: string,
    isOrgView?: boolean,
    resource?: string
  ) => {
    cleanFilterParams();
    if (resource) {
      setPage(1);

      return navigate(`/${resource}?sortBy=${defaultSort}&tabSelected=${tabSelected}`, { replace: false });
    }
    const searchUrl = handleQueryParams({
      page: 1,
      limit: 100,
      searchParams: searchParam,
      sortParams: defaultSort,
      filterParams
    });
    setPage(1);
    navigate(`${pathname}${searchUrl}${chooseFlexible(isOrgView, IS_ORG_VIEW_PARAM, '')}&tabSelected=${tabSelected}`, {
      replace: true
    });
  };

  const [value, setValue] = useState<string>(safeString(tabSelected, ToolsTabsEnum.ALL));
  const menuCurrentValue = safeString(t(`tabsFilter.${tabSelected}`, t('tabsFilter.all')));
  const tabVariant = chooseFlexible(isMyDrive, 'sheets', 'subtle') as TabsProps['variant'];

  if (isMobile)
    return (
      <Box borderRadius="6px" data-testid={COLLECTION_HEADER_MENU_FILTER} maxW="17.438rem" mr={2} w="100%">
        <MenuRoot positioning={{ placement: 'bottom-end' }}>
          <MenuTrigger pb={chooseFlexible(isMyDrive, 3, 0)} w="100%">
            <Flex
              alignItems="center"
              bg="white"
              border="1px solid"
              borderColor="gray100-gray500"
              borderRadius="6px"
              color="text-low-emphasis"
              fontWeight="500"
              height="2.5rem"
              justifyContent="space-between"
              pl={3}
            >
              <Text color="gray.500" data-testid={MENU_CURRENT_VALUE_TEXT} fontSize="sm" fontWeight={500} ml={3}>
                {menuCurrentValue}
              </Text>
              <ArrowDownIcon boxSize={4} color="text-low-emphasis" mr={4} />
            </Flex>
          </MenuTrigger>
          <MenuContent borderBottomRadius="8px" borderTopRadius={0} p={0} transform="translateY(-7px)">
            {Object.keys(tabsConfig).map((key, index) => {
              const tab = tabsConfig[key as keyof typeof tabsConfig];
              const isActive = tabSelected === key;

              return (
                <MenuItem
                  bg={chooseFlexible(isActive, 'smartBlue.50', 'expansiveWhite.500')}
                  borderColor="gray100-gray500"
                  color={chooseFlexible(isActive, 'trustworthyBlue.500', 'relatableGray.500')}
                  data-testid={`${key}-${index}`}
                  fontWeight={chooseFlexible(isActive, 500, 400)}
                  h="4.063rem"
                  key={`${key}-${index}`}
                  px={4}
                  py={2}
                  value={key}
                  w="17.438rem"
                  onClick={() => {
                    setValue(key);
                    filterUrl(tab.value, tab.defaultSort, key, tab.isOrgFilter, tab.resource);
                  }}
                >
                  {tab.title}
                </MenuItem>
              );
            })}
          </MenuContent>
        </MenuRoot>
      </Box>
    );

  return (
    <Box data-testid={COLLECTION_HEADER_TABS_ID}>
      <Tabs.Root
        value={value}
        variant={tabVariant}
        onValueChange={e => {
          setValue(e.value);
        }}
      >
        <Tabs.List>
          {Object.keys(tabsConfig).map(key => {
            const tab = tabsConfig[key as keyof typeof tabsConfig];
            const isActive = tabSelected === key;

            return (
              <TabItem
                counter={tab.counter}
                isActive={isActive}
                isMyDrive={isMyDrive}
                key={key}
                title={tab.title}
                value={key}
                onClick={() => {
                  setValue(key);
                  filterUrl(tab.value, tab.defaultSort, key, tab.isOrgFilter, tab.resource);
                }}
              />
            );
          })}
        </Tabs.List>
      </Tabs.Root>
    </Box>
  );
};
