import { createIcon } from '@ui/icon';

const CompassOutlineIcon = createIcon({
  displayName: 'CompassOutlineIcon',
  viewBox: '0 0 25 24',
  defaultProps: { fill: 'none' },
  path: (
    <>
      <path
        d="M12.4986 22.6047C18.3562 22.6047 23.1047 17.8562 23.1047 11.9986C23.1047 6.14107 18.3562 1.39258 12.4986 1.39258C6.64107 1.39258 1.89258 6.14107 1.89258 11.9986C1.89258 17.8562 6.64107 22.6047 12.4986 22.6047Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2.54545"
      />
      <path
        d="M16.9956 7.50167L14.7471 14.2471L8.00167 16.4956L10.2502 9.75015L16.9956 7.50167Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2.54545"
      />
    </>
  )
});

export default CompassOutlineIcon;
