import { forwardRef } from 'react';
import { Box, Flex } from '@linc-inc/yw-ui/ui';
import { chooseFlexible, withFallback } from '@linc-inc/yw-ui/utils/return-save-data';
import { DISABLED_TAB_INDEX, ENABLED_TAB_INDEX } from './constants';
import { ContentSection } from './content-section';
import { IconContainer } from './icon-container';
import { RadioIndicator } from './radio-indicator';
import { IOptionCardProps } from './types';

export const OptionCard = forwardRef<HTMLDivElement, IOptionCardProps>(
  (
    {
      title,
      subtitle,
      optionKey,
      isSelected = false,
      isDisabled = false,
      icon,
      onSelect,
      contentProps,
      iconBoxProps,
      children,
      ...rest
    },
    ref
  ) => {
    const handleSelect = () => {
      if (!isDisabled && onSelect) {
        onSelect(optionKey);
      }
    };

    return (
      <Flex
        _hover={{
          borderColor: chooseFlexible(!isDisabled, 'creativePurple.100', 'gray.200')
        }}
        aria-checked={isSelected}
        bg={chooseFlexible(isSelected, 'creativePurple.50', 'white')}
        borderColor={chooseFlexible(isSelected, 'creativePurple.100', 'white')}
        borderRadius="0.375rem"
        borderWidth="1px"
        cursor={chooseFlexible(isDisabled, 'not-allowed', 'pointer')}
        direction="column"
        position="relative"
        ref={ref}
        role="radio"
        tabIndex={chooseFlexible(isDisabled, DISABLED_TAB_INDEX, ENABLED_TAB_INDEX)}
        transition="all 0.2s"
        width="full"
        onClick={handleSelect}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleSelect();
          }
        }}
        {...rest}
      >
        <Flex alignItems="center" p={4}>
          <RadioIndicator isDisabled={isDisabled} isSelected={isSelected} />

          {iconBoxProps !== false && (
            <IconContainer
              bg={withFallback(iconBoxProps?.bg, 'gray.300')}
              icon={icon}
              isDisabled={isDisabled}
              {...iconBoxProps}
            />
          )}

          <ContentSection subtitle={subtitle} title={title} {...contentProps} />
        </Flex>

        {children && (
          <Box p={4} pt={0}>
            {children}
          </Box>
        )}
      </Flex>
    );
  }
);

OptionCard.displayName = 'OptionCard';
