import { BoxProps, FlexProps } from '@linc-inc/yw-ui/ui';

export interface IIconContainerProps extends BoxProps {
  icon?: React.ReactNode;
  isDisabled?: boolean;
}

export interface IRadioIndicatorProps extends BoxProps {
  isSelected?: boolean;
  isDisabled?: boolean;
}

export interface IOptionCardProps extends Omit<FlexProps, 'onSelect'> {
  title: string;
  subtitle: string;
  optionKey: string;
  isSelected?: boolean;
  isDisabled?: boolean;
  icon?: React.ReactNode;
  onSelect?: (key: string) => void;
  contentProps?: BoxProps;
  iconBoxProps?: BoxProps | false;
}
