import { Flex } from '@linc-inc/yw-ui/ui';
import { chooseFlexible } from '@linc-inc/yw-ui/utils/return-save-data';
import { ArrowRightIcon } from '@icons';
import { ITopicSubmitButtonProps } from './types';

export const TopicSubmitButton = ({ isDisabled, onClick, ...rest }: ITopicSubmitButtonProps) => {
  return (
    <Flex
      _hover={{
        bg: chooseFlexible(isDisabled, 'purple.400', 'purple.500')
      }}
      alignItems="center"
      aria-disabled={isDisabled}
      as="button"
      bg="creativePurple.500"
      borderRadius={2}
      color="white"
      cursor={chooseFlexible(isDisabled, 'not-allowed', 'pointer')}
      height="1.875rem"
      justifyContent="center"
      overflow="hidden"
      width="1.875rem"
      onClick={e => {
        e.stopPropagation();
        onClick(e);
      }}
      {...rest}
    >
      <ArrowRightIcon boxSize={4} />
    </Flex>
  );
};
