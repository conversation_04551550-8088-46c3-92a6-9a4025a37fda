import { FlexProps, InputProps } from '@linc-inc/yw-ui/ui';
import { IOptionCardProps } from '../option-card/types';

export type TopicPlaceholderType = {
  placeholder?: string;
};

export type TopicInputSectionType = {
  topicValue: string;
  setTopicValue: (value: string) => void;
  handleTopicSubmit: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
};

export interface ITopicInputProps extends Omit<InputProps, 'onChange' | 'onKeyDown' | 'onClick' | 'value'> {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  onClick: (e: React.MouseEvent) => void;
}

export interface ITopicSubmitButtonProps extends Omit<FlexProps, 'onClick'> {
  isDisabled: boolean;
  onClick: (e: React.MouseEvent) => void;
}

export interface ITopicOptionProps extends Omit<IOptionCardProps, 'onSubmit' | 'title' | 'subtitle' | 'optionKey'> {
  onSubmit?: (value: string) => void;
  title: string;
  subtitle: string;
  optionKey?: string;
  placeholder?: string;
}
