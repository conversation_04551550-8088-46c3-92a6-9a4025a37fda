import { useState } from 'react';
import { Box } from '@linc-inc/yw-ui/ui';
import { CompassOutlineIcon } from '@icons';
import { OptionCard } from '../option-card';
import { TopicInputSection } from './topic-input-section';
import { TopicPlaceholder } from './topic-placeholder';
import { ITopicOptionProps } from './types';

export const TopicOption = ({
  isSelected,
  onSubmit,
  onSelect,
  optionKey = 'topic',
  title,
  subtitle,
  placeholder,
  ...rest
}: ITopicOptionProps) => {
  const [topicValue, setTopicValue] = useState('');

  const handleTopicSubmit = () => {
    if (topicValue.trim() && onSubmit) {
      onSubmit(topicValue);
      setTopicValue('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTopicSubmit();
    }
  };

  return (
    <OptionCard
      icon={<CompassOutlineIcon height="1.5rem" width="1.5rem" />}
      iconBoxProps={{ bg: 'creativePurple.300', color: 'white' }}
      isSelected={isSelected}
      optionKey={optionKey}
      subtitle={subtitle}
      title={title}
      onSelect={onSelect}
      {...rest}
    >
      {isSelected && (
        <Box>
          <TopicPlaceholder placeholder={placeholder} />
          <TopicInputSection
            handleKeyDown={handleKeyDown}
            handleTopicSubmit={handleTopicSubmit}
            setTopicValue={setTopicValue}
            topicValue={topicValue}
          />
        </Box>
      )}
    </OptionCard>
  );
};
