import { Input } from '@linc-inc/yw-ui/ui';
import { ITopicInputProps } from './types';

export const TopicInput = ({ value, onChange, onKeyDown, onClick, ...rest }: ITopicInputProps) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === ' ') {
      e.stopPropagation();
    }
    onKeyDown(e);
  };

  return (
    <Input
      _focus={{
        borderColor: 'gray.300'
      }}
      _focusVisible={{
        outlineWidth: 0
      }}
      bg="white"
      borderColor="gray.200"
      value={value}
      onChange={onChange}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      {...rest}
    />
  );
};
