import { fireEvent, screen } from '@testing-library/react';
import { renderWithProviders } from '@tests/helpers';
import { TopicOption } from './topic-option';

describe('TopicOption', () => {
  const defaultProps = {
    title: 'Test Topic',
    subtitle: 'Test Subtitle',
    placeholder: 'Enter a topic'
  };

  it('handles selection with different optionKeys', () => {
    const onSelectMock = jest.fn();
    const { rerender } = renderWithProviders(<TopicOption {...defaultProps} onSelect={onSelectMock} />, {
      useChakra: true
    });

    fireEvent.click(screen.getByRole('radio'));
    expect(onSelectMock).toHaveBeenCalledWith('topic');
    onSelectMock.mockClear();

    rerender(<TopicOption {...defaultProps} optionKey="custom-topic" onSelect={onSelectMock} />);
    fireEvent.click(screen.getByRole('radio'));
    expect(onSelectMock).toHaveBeenCalledWith('custom-topic');
  });

  it('updates input value when typing', () => {
    renderWithProviders(<TopicOption {...defaultProps} isSelected />, { useChakra: true });

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'New Topic' } });

    expect(input).toHaveValue('New Topic');
  });

  it('handles submission via button click and Enter key', () => {
    const onSubmitMock = jest.fn();
    renderWithProviders(<TopicOption {...defaultProps} isSelected onSubmit={onSubmitMock} />, {
      useChakra: true
    });

    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button');

    fireEvent.change(input, { target: { value: 'New Topic' } });
    fireEvent.click(submitButton);
    expect(onSubmitMock).toHaveBeenCalledWith('New Topic');
    expect(input).toHaveValue('');
    onSubmitMock.mockClear();

    fireEvent.change(input, { target: { value: 'Another Topic' } });
    fireEvent.keyDown(input, { key: 'Enter' });
    expect(onSubmitMock).toHaveBeenCalledWith('Another Topic');
    expect(input).toHaveValue('');
  });

  it('prevents submission of empty or whitespace-only input', () => {
    const onSubmitMock = jest.fn();
    renderWithProviders(<TopicOption {...defaultProps} isSelected onSubmit={onSubmitMock} />, {
      useChakra: true
    });

    const input = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button');

    fireEvent.click(submitButton);
    expect(onSubmitMock).not.toHaveBeenCalled();

    fireEvent.change(input, { target: { value: '   ' } });
    fireEvent.click(submitButton);
    expect(onSubmitMock).not.toHaveBeenCalled();
  });
});
