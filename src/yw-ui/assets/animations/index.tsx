import { CSSProperties, lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import <PERSON><PERSON> from 'lottie-light-react';

// Lazy load all animation JSON files
const lincCharacterCoachAnimation = () => import('./linc-character-coach.json');
const lincCharacterComputerAnimation = () => import('./linc-character-computer.json');
const lincCharacterConsultantAnimation = () => import('./linc-character-consultant.json');
const lincCharacterCoordinatorAnimation = () => import('./linc-character-coordinator.json');
const lincCharacterHiFive = () => import('./linc-character-hi-five.json');
const lincCharacterLeaderAnimation = () => import('./linc-character-leader.json');
const lincCharacterLoading = () => import('./linc-character-loading.json');
const lincCharacterLoginAnimation = () => import('./linc-character-login.json');
const lincCharacterMegaphoneAnimation = () => import('./linc-character-megaphone.json');
const lincCharacterOopsAnimation = () => import('./linc-character-oops.json');
const lincCharacterOtherAnimation = () => import('./linc-character-other.json');
const lincCharacterRolePickerAnimation = () => import('./linc-character-role-picker.json');
const lincCharacterSmileAnimation = () => import('./linc-character-smile.json');
const lincCharacterTeacherAnimation = () => import('./linc-character-teacher.json');
const lincCharacterThinkingAnimation = () => import('./linc-character-thinking.json');
const lincCharacterThisIsFineAnimation = () => import('./linc-character-this-is-fine.json');
const lincCharacterToolCover = () => import('./linc-character-tool-cover.json');
const lincCharacterVacation = () => import('./linc-character-vacations.json');
const beansRocketAnimation = () => import('./beans-rocket.json');

type AnimationType = {
  style?: CSSProperties;
  alt?: string;
};

const useLottieAlt = (alt: string | undefined): string => {
  const { t } = useTranslation();

  return alt ?? t('common:generic.imgAltText');
};

const defaultStyles = { height: '100%', width: '100%' };

// Loading fallback component
const LoadingFallback = () => <div style={defaultStyles} />;

// Utility function to create a lazy-loaded animation component with Suspense wrapper
const createAnimation = (importFn: () => Promise<any>, renderer?: 'svg') => {
  const LazyComponent = lazy(async () => {
    const animationData = await importFn();
    const AnimationComponent = ({ style = defaultStyles, alt }: AnimationType) => {
      const animationAlt = useLottieAlt(alt);

      return <Lottie alt={animationAlt} animationData={animationData.default} renderer={renderer} style={style} />;
    };
    AnimationComponent.displayName = 'AnimationComponent';

    return { default: AnimationComponent };
  });

  const Wrapper = (props: AnimationType) => (
    <Suspense fallback={<LoadingFallback />}>
      <LazyComponent {...props} />
    </Suspense>
  );
  Wrapper.displayName = 'AnimationWrapper';

  return Wrapper;
};

// Create animation components
export const LincCharacterCoachAnimationWrapper = createAnimation(lincCharacterCoachAnimation);
export const LincCharacterMegaphoneAnimation = createAnimation(lincCharacterMegaphoneAnimation);
export const LincCharacterComputerAnimation = createAnimation(lincCharacterComputerAnimation);
export const LincCharacterConsultantAnimation = createAnimation(lincCharacterConsultantAnimation);
export const LincCharacterCoordinatorAnimation = createAnimation(lincCharacterCoordinatorAnimation);
export const LincCharacterLeaderAnimation = createAnimation(lincCharacterLeaderAnimation);
export const LincCharacterLoginAnimation = createAnimation(lincCharacterLoginAnimation);
export const LincCharacterOopsAnimation = createAnimation(lincCharacterOopsAnimation);
export const LincCharacterOtherAnimation = createAnimation(lincCharacterOtherAnimation);
export const LincCharacterRolePickerAnimation = createAnimation(lincCharacterRolePickerAnimation);
export const LincCharacterSmileAnimation = createAnimation(lincCharacterSmileAnimation);
export const LincCharacterTeacherAnimation = createAnimation(lincCharacterTeacherAnimation);
export const LincCharacterThinkingAnimation = createAnimation(lincCharacterThinkingAnimation);
export const LincCharacterThisIsFineAnimation = createAnimation(lincCharacterThisIsFineAnimation);
export const LincCharacterHiFiveAnimation = createAnimation(lincCharacterHiFive);
export const LincCharacterVacationAnimation = createAnimation(lincCharacterVacation);
export const LincCharacterToolCoverAnimation = createAnimation(lincCharacterToolCover, 'svg');
export const LincCharacterLoadingAnimation = createAnimation(lincCharacterLoading);
export const BeansRocketAnimation = createAnimation(beansRocketAnimation);
