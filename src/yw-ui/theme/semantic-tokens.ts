import { defineSemanticTokens } from '@chakra-ui-v3/react';
import { SECTION_CONTAINER_COLOR } from '@constants';

const WHITE = '{colors.expansiveWhite.500}';
const TRUSTWORTHY_BLUE_500 = '{colors.trustworthyBlue.500}';
const RELATABLE_GRAY_100 = '{colors.relatableGray.100}';
const RELATABLE_GRAY_800 = '{colors.relatableGray.800}';
const RELATABLE_GRAY_500 = '{colors.relatableGray.500}';
const RELATABLE_GRAY_600 = '{colors.relatableGray.600}';
const RELATABLE_GRAY_900 = '{colors.relatableGray.900}';
const CRIMSON_RED_600 = '{colors.crimsonRed.600}';
const CRIMSON_RED_200 = '{colors.crimsonRed.200}';
const BLUE_50 = '{colors.trustworthyBlue.50}';

export const SEMANTIC_TOKENS = defineSemanticTokens({
  colors: {
    'text-high-emphasis': {
      value: { base: '{colors.text.highEmphasis.light}', _dark: '{colors.text.highEmphasis.dark}' }
    },
    'text-medium-emphasis': {
      value: { base: '{colors.text.mediumEmphasis.light}', _dark: '{colors.text.mediumEmphasis.dark}' }
    },
    'text-low-emphasis': {
      value: { base: '{colors.text.lowEmphasis.light}', _dark: '{colors.text.lowEmphasis.dark}' }
    },
    'feedback-inspiring-red': {
      value: { base: CRIMSON_RED_600, _dark: CRIMSON_RED_200 }
    },
    trustworthyBlue: {
      focusRing: { value: '{colors.focusRing.light}' }
    },
    background: {
      value: { base: '{colors.background.light}', _dark: '{colors.background.dark}' }
    },
    'border-color': {
      value: { base: '{colors.borderColor.light}', _dark: '{colors.borderColor.dark}' }
    },
    'gray-bg': {
      value: { base: '{colors.grayBg.light}' }
    },
    'white50-gray800': {
      value: { base: '{colors.expansiveWhite.50}', _dark: RELATABLE_GRAY_800 }
    },
    'blue500-white500': {
      value: { base: TRUSTWORTHY_BLUE_500, _dark: WHITE }
    },
    'white500-gray500': {
      value: { base: WHITE, _dark: RELATABLE_GRAY_500 }
    },
    'white500-gray800': {
      value: { base: WHITE, _dark: RELATABLE_GRAY_800 }
    },
    'gray100-gray700': {
      value: { base: RELATABLE_GRAY_100, _dark: '{colors.relatableGray.700}' }
    },
    'gray100-gray800': {
      value: { base: RELATABLE_GRAY_100, _dark: RELATABLE_GRAY_800 }
    },
    'blue100-gray800': {
      value: { base: '{colors.trustworthyBlue.100}', _dark: RELATABLE_GRAY_800 }
    },
    'blue50-gray800': {
      value: { base: BLUE_50, _dark: RELATABLE_GRAY_800 }
    },
    'blue50-gray600': {
      value: { base: BLUE_50, _dark: '{colors.relatableGray.600}' }
    },
    'gray100-gray500': {
      value: { base: RELATABLE_GRAY_100, _dark: RELATABLE_GRAY_500 }
    },
    'gray100-gray600': {
      value: { base: RELATABLE_GRAY_100, _dark: RELATABLE_GRAY_600 }
    },
    'white500-gray900': {
      value: { base: WHITE, _dark: RELATABLE_GRAY_900 }
    },
    'blue500-blue200': {
      value: { base: '{colors.trustworthyBlue.500}', _dark: '{colors.trustworthyBlue.200}' }
    },
    'blue200-blue500': {
      value: { base: '{colors.trustworthyBlue.200}', _dark: '{colors.trustworthyBlue.500}' }
    },
    'blue50-blue200': {
      value: { base: '{colors.trustworthyBlue.50}', _dark: '{colors.trustworthyBlue.200}' }
    },
    'highlight-item': {
      value: { base: SECTION_CONTAINER_COLOR, _dark: RELATABLE_GRAY_500 }
    },
    'blue50-gray500': {
      value: { base: BLUE_50, _dark: RELATABLE_GRAY_500 }
    },
    'gray600-gray300': {
      value: { base: '{colors.relatableGray.600}', _dark: '{colors.relatableGray.300}' }
    },
    'gray700-gray200': {
      value: { base: '{colors.relatableGray.700}', _dark: '{colors.relatableGray.200}' }
    },
    'gray900-gray50': {
      value: { base: RELATABLE_GRAY_900, _dark: '{colors.relatableGray.50}' }
    },
    'green600-green500': {
      value: { base: '{colors.helpfulGreen.600}', _dark: '{colors.helpfulGreen.500}' }
    },
    'red600-red200': {
      value: { base: '{colors.crimsonRed.600}', _dark: '{colors.crimsonRed.200}' }
    },
    'blue500-gray600': {
      value: { base: TRUSTWORTHY_BLUE_500, _dark: RELATABLE_GRAY_600 }
    },
    'smart50-gray700': {
      value: { base: '{colors.smartBlue.50}', _dark: '{colors.relatableGray.700}' }
    }
  }
});
