import { defineTokens } from '@chakra-ui-v3/react';

export const TOKENS = defineTokens({
  colors: {
    text: {
      highEmphasis: {
        light: { value: '#1E2327' },
        dark: { value: '#E8E8E8' }
      },
      mediumEmphasis: {
        light: { value: '#4B4F52' },
        dark: { value: '#B5B5B5' }
      },
      lowEmphasis: {
        light: { value: '#686C6E' },
        dark: { value: '#8C8C8C' }
      }
    },
    background: {
      light: { value: '#F4F6FB' },
      dark: { value: '#1E2327' }
    },
    backgroundOnHover: {
      light: { value: '#F2F5FF' }
    },
    backgroundOnFocus: {
      light: { value: '#f0f3ff' }
    },
    borderColor: {
      light: { value: '#D7DCE0' },
      dark: { value: '#697884' }
    },
    grayBg: {
      light: { value: '#FAFAFA' }
    },
    focusRing: {
      light: { value: '#4299e199' }
    },
    trustworthyBlue: {
      50: { value: '#F3F5FF' },
      100: { value: '#C4CEF7' },
      200: { value: '#A8B6F4' },
      300: { value: '#8094EE' },
      400: { value: '#6880EB' },
      500: { value: '#4260E6' },
      600: { value: '#3C57D1' },
      700: { value: '#2F44A3' },
      800: { value: '#24357F' },
      900: { value: '#1C2861' }
    },
    smartBlue: {
      50: { value: '#E7EAF2' },
      100: { value: '#B6BFD6' },
      200: { value: '#92A0C3' },
      300: { value: '#6075A7' },
      400: { value: '#415A96' },
      500: { value: '#12317C' },
      600: { value: '#102D71' },
      700: { value: '#0D2358' },
      800: { value: '#0A1B44' },
      900: { value: '#081534' }
    },
    expansiveWhite: {
      50: { value: '#F2F2F2' },
      100: { value: '#DBDBDB' },
      200: { value: '#C4C4C4' },
      300: { value: '#ADADAD' },
      400: { value: '#969696' },
      500: { value: '#FFFFFF' },
      600: { value: '#E8E8E8' },
      700: { value: '#B5B5B5' },
      800: { value: '#8C8C8C' },
      900: { value: '#6B6B6B' }
    },
    relatableGray: {
      50: { value: '#E9E9E9' },
      100: { value: '#B9BBBC' },
      200: { value: '#989A9C' },
      300: { value: '#686C6E' },
      400: { value: '#4B4F52' },
      500: { value: '#1E2327' },
      600: { value: '#1B2023' },
      700: { value: '#15191C' },
      800: { value: '#111315' },
      900: { value: '#0D0F10' }
    },
    creativePurple: {
      50: { value: '#F5EFFB' },
      100: { value: '#E0CEF4' },
      200: { value: '#D1B6EE' },
      300: { value: '#BD95E6' },
      400: { value: '#B081E1' },
      500: { value: '#9C61DA' },
      600: { value: '#8E58C6' },
      700: { value: '#6F459B' },
      800: { value: '#563578' },
      900: { value: '#42295C' }
    },
    beansPurple: {
      50: { value: '#FAF1FE' },
      100: { value: '#F0D4FD' },
      200: { value: '#E8BFFB' },
      300: { value: '#DEA2FA' },
      400: { value: '#D790F9' },
      500: { value: '#CD74F7' },
      600: { value: '#BB6AE1' },
      700: { value: '#9252AF' },
      800: { value: '#714088' },
      900: { value: '#563168' }
    },
    companionPink: {
      50: { value: '#FAEFF8' },
      100: { value: '#F0CEE8' },
      200: { value: '#E8B6DD' },
      300: { value: '#DE94CD' },
      400: { value: '#D780C4' },
      500: { value: '#CD60B5' },
      600: { value: '#BB57A5' },
      700: { value: '#924481' },
      800: { value: '#713564' },
      900: { value: '#56284C' }
    },
    crimsonRed: {
      50: { value: '#FEE6E8' },
      100: { value: '#FDBABE' },
      200: { value: '#FB8D94' },
      300: { value: '#FA616B' },
      400: { value: '#F93441' },
      500: { value: '#C60613' },
      600: { value: '#94050E' },
      700: { value: '#630309' },
      800: { value: '#310205' },
      900: { value: '#310205' }
    },
    inspiringRed: {
      50: { value: '#FFEFF0' },
      100: { value: '#FDCED1' },
      200: { value: '#FDB6BA' },
      300: { value: '#FC949B' },
      400: { value: '#FB8088' },
      500: { value: '#FA606A' },
      600: { value: '#E45760' },
      700: { value: '#B2444B' },
      800: { value: '#8A353A' },
      900: { value: '#69282D' }
    },
    excitingOrange: {
      50: { value: '#FEF5EF' },
      100: { value: '#FBE1CC' },
      200: { value: '#F9D3B3' },
      300: { value: '#F6BF90' },
      400: { value: '#F4B27B' },
      500: { value: '#F19F5A' },
      600: { value: '#DB9152' },
      700: { value: '#AB7140' },
      800: { value: '#855732' },
      900: { value: '#654326' }
    },
    approachableYellow: {
      50: { value: '#FEFBEE' },
      100: { value: '#FDF1CA' },
      200: { value: '#FCEBB0' },
      300: { value: '#FBE28C' },
      400: { value: '#FADC76' },
      500: { value: '#F9D354' },
      600: { value: '#E3C04C' },
      700: { value: '#B1963C' },
      800: { value: '#89742E' },
      900: { value: '#695923' }
    },
    helpfulGreen: {
      50: { value: '#F6FBF3' },
      100: { value: '#E2F3DA' },
      200: { value: '#D4EDC8' },
      300: { value: '#C0E4AF' },
      400: { value: '#B4DFA0' },
      500: { value: '#A1D788' },
      600: { value: '#93C47C' },
      700: { value: '#729961' },
      800: { value: '#59764B' },
      900: { value: '#3C541A' }
    },
    gleefulGreen: {
      50: { value: '#F4F9EC' },
      100: { value: '#DCEEC3' },
      200: { value: '#CBE5A7' },
      300: { value: '#B3D97E' },
      400: { value: '#A5D265' },
      500: { value: '#8EC73F' },
      600: { value: '#81B539' },
      700: { value: '#658D2D' },
      800: { value: '#4E6D23' },
      900: { value: '#3C541A' }
    },
    informativeBlue: {
      50: { value: '#E5F5FF' },
      100: { value: '#CCEBFF' },
      200: { value: '#99D8FF' },
      300: { value: '#66C4FF' },
      400: { value: '#33B1FF' },
      500: { value: '#009DFF' },
      600: { value: '#007ECC' },
      700: { value: '#005E99' },
      800: { value: '#003F66' },
      900: { value: '#001F33' }
    }
  }
});
