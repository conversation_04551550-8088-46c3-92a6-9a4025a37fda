import { defineSlotRecipe } from '@chakra-ui-v3/react';

export const CUSTOM_ALERT_SLOT_RECIPE = defineSlotRecipe({
  slots: ['alert', 'title', 'description', 'content', 'indicator', 'closeButton'],
  base: {
    alert: {
      bg: 'colorPalette.50',
      overflow: 'hidden',
      paddingY: '0.75rem',
      paddingX: '1rem',
      minWidth: '17rem',
      rounded: 'sm',
      '&::after': {
        content: '""',
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        display: 'block',
        width: 1,
        height: 'full',
        bg: 'colorPalette.600'
      }
    },
    indicator: {
      color: 'colorPalette.500',
      alignSelf: 'center',
      width: '1.5rem',
      height: '1.5rem'
    },
    closeButton: {
      bg: 'transparent',
      color: 'relatableGray.800',
      '& > .chakra-icon': {
        width: '0.75rem',
        height: '0.75rem'
      }
    },
    title: {
      fontSize: 'sm',
      fontWeight: '700',
      lineHeight: '120%',
      color: 'relatableGray.800'
    },
    description: {
      fontSize: 'sm',
      fontWeight: '400',
      lineHeight: '120%',
      color: 'relatableGray.800'
    },
    content: {
      color: 'relatableGray.800',
      gap: '0',
      justifyContent: 'center',
      minHeight: '2rem'
    }
  },
  variants: {
    status: {
      info: {
        alert: {
          colorPalette: 'trustworthyBlue'
        },
        indicator: {
          colorPalette: 'trustworthyBlue'
        }
      },
      success: {
        alert: {
          colorPalette: 'helpfulGreen',
          '&::after': {
            bg: 'colorPalette.700'
          }
        },
        indicator: {
          colorPalette: 'helpfulGreen',
          color: 'colorPalette.700'
        }
      },
      warning: {
        alert: {
          colorPalette: 'excitingOrange',
          '&::after': {
            bg: 'colorPalette.500'
          }
        },
        indicator: {
          colorPalette: 'excitingOrange'
        }
      },
      error: {
        alert: {
          colorPalette: 'crimsonRed',
          '&::after': {
            bg: 'colorPalette.500'
          }
        },
        indicator: {
          colorPalette: 'crimsonRed',
          color: 'colorPalette.700'
        }
      }
    }
  },
  defaultVariants: {
    status: 'info'
  }
});
