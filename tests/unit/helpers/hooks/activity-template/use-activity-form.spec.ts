import { act, renderHook } from '@testing-library/react';
import { useActivityForm } from '@helpers/hooks/activity-template/use-activity-form';

describe('useActivityForm', () => {
  it('should initialize with default values and update classId when setClass is called', () => {
    const { result } = renderHook(() => useActivityForm());

    expect(result.current.formState).toEqual({
      classId: '',
      time: '5',
      sourceType: 'topic',
      objective: ''
    });
    expect(result.current.isValid).toBe(false);

    act(() => {
      result.current.setClass('class1');
    });

    expect(result.current.formState.classId).toBe('class1');
  });

  it('should update time when setTime is called', () => {
    const { result } = renderHook(() => useActivityForm());

    act(() => {
      result.current.setTime('10');
    });

    expect(result.current.formState.time).toBe('10');
  });

  it('should update sourceType when setSourceType is called', () => {
    const { result } = renderHook(() => useActivityForm());

    act(() => {
      result.current.setSourceType('file');
    });

    expect(result.current.formState.sourceType).toBe('file');
  });

  it('should update objective when setObjective is called', () => {
    const { result } = renderHook(() => useActivityForm());

    act(() => {
      result.current.setObjective('Test objective');
    });

    expect(result.current.formState.objective).toBe('Test objective');
  });

  it('should reset form state when reset is called', () => {
    const { result } = renderHook(() => useActivityForm());

    act(() => {
      result.current.setClass('class1');
      result.current.setTime('10');
      result.current.setSourceType('file');
      result.current.setObjective('Test objective');
      result.current.reset();
    });

    expect(result.current.formState).toEqual({
      classId: '',
      time: '5',
      sourceType: 'topic',
      objective: ''
    });
  });
});
