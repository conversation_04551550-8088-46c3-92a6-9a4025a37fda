import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  ACTIVITY_TEMPLATE_BUTTON_TEST_ID,
  ActivityTemplateButton
} from '@containers/activity-template/activity-template-button';
import { renderWithProviders } from '@tests/helpers';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  useTranslation: jest.fn().mockReturnValue({ t: (key: string) => key })
}));

jest.mock('@helpers/hooks/activity-template/use-activity-form', () => ({
  useActivityForm: jest.fn().mockReturnValue({
    formState: {
      classId: '',
      time: '5',
      sourceType: 'topic',
      objective: ''
    },
    isValid: false,
    reset: jest.fn()
  })
}));

describe('ActivityTemplateButton', () => {
  const user = userEvent.setup();
  const buttonContent = 'Activity Template';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('opens the modal when button is clicked', async () => {
    renderWithProviders(<ActivityTemplateButton>{buttonContent}</ActivityTemplateButton>, {
      useChakra: true,
      useRedux: true
    });

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();

    const button = screen.getByTestId(ACTIVITY_TEMPLATE_BUTTON_TEST_ID);
    await user.click(button);

    await waitFor(() => {
      expect(screen.getAllByRole('dialog').length).toBeGreaterThan(0);
    });
  });

  it('closes the modal when clicking the close button', async () => {
    renderWithProviders(<ActivityTemplateButton>{buttonContent}</ActivityTemplateButton>, {
      useChakra: true,
      useRedux: true
    });

    const button = screen.getByTestId(ACTIVITY_TEMPLATE_BUTTON_TEST_ID);
    await user.click(button);

    await waitFor(() => {
      expect(screen.getAllByRole('dialog').length).toBeGreaterThan(0);
    });

    const backButton = screen.getByText('buttons.back');
    await user.click(backButton);

    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });
});
