import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ActivitySetupModal } from '@containers/activity-template/activity-setup-modal';
import { renderWithProviders } from '@tests/helpers';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  useTranslation: jest.fn().mockReturnValue({ t: (key: string) => key })
}));

jest.mock('@helpers/hooks/activity-template/use-activity-form', () => ({
  useActivityForm: jest.fn().mockReturnValue({
    formState: {
      classId: 'class1',
      time: '5',
      sourceType: 'topic',
      objective: 'Test objective'
    },
    isValid: true,
    reset: jest.fn(),
    setClass: jest.fn(),
    setTime: jest.fn(),
    setSourceType: jest.fn(),
    setObjective: jest.fn()
  })
}));

describe('ActivitySetupModal', () => {
  const user = userEvent.setup();
  const mockOnClose = jest.fn();
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the modal when isOpen is true', async () => {
    renderWithProviders(<ActivitySetupModal isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />, {
      useChakra: true,
      useRedux: true
    });

    await waitFor(() => {
      expect(screen.getAllByRole('dialog').length).toBeGreaterThan(0);
    });

    expect(screen.getByText('title')).toBeInTheDocument();
    expect(screen.getByText('description')).toBeInTheDocument();
  });

  it('does not render the modal when isOpen is false', () => {
    renderWithProviders(<ActivitySetupModal isOpen={false} onClose={mockOnClose} onSubmit={mockOnSubmit} />, {
      useChakra: true,
      useRedux: true
    });

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('calls onClose when clicking the back button in first step', async () => {
    renderWithProviders(<ActivitySetupModal isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />, {
      useChakra: true,
      useRedux: true
    });

    const backButton = await screen.findByText('buttons.back');
    await user.click(backButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when clicking the close button', async () => {
    renderWithProviders(<ActivitySetupModal isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />, {
      useChakra: true,
      useRedux: true
    });

    const closeButton = await screen.findByRole('button', { name: /close/i });
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
});
